# a<PERSON><PERSON><PERSON>l Python Backend 开发环境配置

# 环境设置
DEBUG_FLAG=true
ENVIRONMENT=development

# 应用配置
HOST_UPLOADS=http://**************:3030/uploads

# 存储配置
PROJECT_SAVE_DIR=/nfs5/medlabel/medlabel_dev_yjb/projects

# RabbitMQ 配置
RABBITMQ_HOST=**************
RABBITMQ_PORT=25675
RABBITMQ_ACCOUNT=admin
RABBITMQ_PASSWORD=vipa@404
RABBITMQ_CONNECTION_TIMEOUT=60
RABBITMQ_HEARTBEAT=300
RABBITMQ_SOCKET_TIMEOUT=10
RABBITMQ_BLOCKED_CONNECTION_TIMEOUT=300
RABBITMQ_CONNECTION_ATTEMPTS=3
RABBITMQ_RETRY_DELAY=1

# Redis 配置
REDIS_HOST=**************
REDIS_PORT=26380
REDIS_PASSWORD=
REDIS_DB=0

# 日志配置
LOG_LEVEL=DEBUG
LOG_DIR=logdev

# GPU 配置
GPU_MEMORY_THRESHOLD=0.8

# Celery 配置
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=Asia/Shanghai
CELERY_ENABLE_UTC=true
