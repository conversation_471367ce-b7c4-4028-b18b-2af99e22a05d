# 系统组件详细说明

## 概述

本文档详细描述了 aiLabel_python_backend 系统中各个组件的功能、实现细节和交互方式。

## 🏗️ 系统组件架构

```mermaid
graph TB
    subgraph "配置管理层"
        Config[config.py<br/>配置中心]
        EnvDev[.env.dev<br/>开发环境]
        EnvProd[.env.prod<br/>生产环境]
    end
    
    subgraph "应用服务层"
        AppInit[app/__init__.py<br/>应用初始化]
        ImageProcess[app/image_process.py<br/>图像处理服务]
        KFBReader[app/kfbreader.py<br/>KFB读取器]
    end
    
    subgraph "算法工具层"
        GPUUtils[asm/utils/gpu.py<br/>GPU管理]
        PolygonUtils[asm/utils/polygon.py<br/>多边形处理]
        UtilUtils[asm/utils/util.py<br/>通用工具]
    end
    
    subgraph "外部服务"
        RabbitMQ[(RabbitMQ<br/>消息队列)]
        Redis[(Redis<br/>缓存)]
        NFS[(NFS<br/>文件存储)]
    end
    
    Config --> AppInit
    Config --> GPUUtils
    Config --> UtilUtils
    
    AppInit --> ImageProcess
    ImageProcess --> KFBReader
    ImageProcess --> PolygonUtils
    
    AppInit --> RabbitMQ
    AppInit --> Redis
    ImageProcess --> NFS
```

## 📋 组件详细说明

### 1. 配置管理层

#### config.py - 配置中心
**功能**: 统一管理系统配置，支持多环境切换

**核心配置项**:
```python
# 环境配置
CELERY_ENV = os.getenv('CELERY_ENV', 'prod')  # dev/prod

# RabbitMQ 配置
RABBITMQ_HOST = '**************'
RABBITMQ_PORT = 25675
RABBITMQ_ACCOUNT = 'admin'
RABBITMQ_PASSWORD = 'vipa@404'

# Redis 配置
REDIS_HOST = '**************'
REDIS_PORT = 26379

# 存储配置
PROJECT_SAVE_DIR = '/nfs5/medlabel/medlabel_212/projects'
```

**环境切换逻辑**:
1. 检查 `CELERY_ENV` 环境变量
2. 优先加载对应环境的配置文件
3. 回退到默认配置

### 2. 应用服务层

#### app/__init__.py - 应用初始化
**功能**: Celery 应用创建、RabbitMQ 连接管理、多线程消费者启动

**核心功能**:
```python
def create_celery() -> Celery:
    """创建并配置 Celery 实例"""
    celery = Celery('aiLabel_backend')
    celery.config_from_object({
        'broker_url': f'pyamqp://{RABBITMQ_ACCOUNT}:{RABBITMQ_PASSWORD}@{RABBITMQ_HOST}:{RABBITMQ_PORT}//',
        'result_backend': f'redis://{REDIS_HOST}:{REDIS_PORT}/0',
        'worker_prefetch_multiplier': 1,
        'task_soft_time_limit': 1800,
        'task_time_limit': 2400,
    })
    return celery
```

**多线程消费者**:
- 启动 5 个并发线程处理图像转换任务
- 每个线程独立处理队列消息
- 支持手动消息确认机制

#### app/image_process.py - 图像处理服务
**功能**: 核心图像处理逻辑，支持多种医学图像格式转换

**支持的图像类型**:
1. **自然图像** (imageTypeId: 1)
   - 格式: PNG, JPG, JPEG
   - 处理: 直接复制到目标目录

2. **DICOM 医学图像** (imageTypeId: 2)
   - 格式: DICOM, DCM
   - 处理: 使用 SimpleITK 进行格式转换和归一化

3. **病理图像** (imageTypeId: 3)
   - 格式: SVS, TIF, TIFF, KFB
   - 处理: 生成 Deep Zoom 金字塔结构

4. **多通道图像** (imageTypeId: 4)
   - 格式: QPTIFF
   - 处理: 通道分离和并行处理

**核心处理流程**:
```python
def image_process(channel, method, properties, body):
    """主要图像处理入口"""
    data = json.loads(body)
    imageTypeId = data.get('imageTypeId')
    
    if imageTypeId == 1:
        normal_image_process(data, channel)
    elif imageTypeId == 2:
        dicom_image_process(data, channel)
    elif imageTypeId == 3:
        patho_image_process(data, channel)
    elif imageTypeId == 4:
        channel_image_process(data, channel)
```

#### app/kfbreader.py - KFB 读取器
**功能**: 专门处理 KFB 格式的病理图像文件

**核心特性**:
- 基于 OpenSlide 架构的扩展
- 支持多层级图像读取
- 提供区域裁剪功能
- 兼容 OpenSlide 接口

### 3. 算法工具层

#### asm/utils/gpu.py - GPU 管理
**功能**: 自动选择和管理 GPU 资源

**核心算法**:
```python
def seek_gpu():
    """选择空闲显存最大的 GPU"""
    pynvml.nvmlInit()
    GPUCount = pynvml.nvmlDeviceGetCount()
    
    choose_gid = 0
    max_free_memory = 0
    
    for gid in range(GPUCount):
        handle = pynvml.nvmlDeviceGetHandleByIndex(gid)
        meminfo = pynvml.nvmlDeviceGetMemoryInfo(handle)
        if max_free_memory < meminfo.free / 1024**3:
            max_free_memory = meminfo.free / 1024**3
            choose_gid = gid
    
    os.environ["CUDA_VISIBLE_DEVICES"] = str(choose_gid)
```

#### asm/utils/polygon.py - 多边形处理
**功能**: 图像轮廓检测和多边形处理算法

**核心功能**:
1. **轮廓检测**: 使用 OpenCV 进行边界检测
2. **多边形简化**: 实现 Douglas-Peucker 算法
3. **SAM 结果处理**: 处理 Segment Anything 模型输出
4. **格式转换**: 转换为前端可用的标注格式

**主要函数**:
```python
def get_polygon(label, sample="Dynamic"):
    """从二值图像提取多边形轮廓"""
    
def find_board_V2(img):
    """从语义分割结果提取边界点"""
    
def sam_find_board_point(masks):
    """处理 SAM 模型输出的掩码"""
```

#### asm/utils/util.py - 通用工具
**功能**: 提供通用的工具函数和性能监控

**核心工具**:
1. **Timer 类**: 性能计时和监控
2. **路径映射**: 图像路径转换
3. **日志格式化**: 统一的日志输出格式

### 4. 外部服务集成

#### RabbitMQ 消息队列
**配置**:
- 主机: **************:25675
- 队列: `medlabel_image_convert_queue`
- 持久化: 支持消息持久化
- 死信队列: 配置错误消息处理

**消息格式**:
```json
{
    "taskId": "unique_task_id",
    "projectId": 123,
    "imageName": "sample_image",
    "imageTypeId": 3,
    "imageId": 456,
    "imageUrl": "/path/to/image.svs"
}
```

#### Redis 缓存
**用途**:
- Celery 结果后端存储
- 任务状态缓存
- 临时数据存储

**配置**:
- 主机: **************:26379
- 数据库: 0 (默认)
- 过期时间: 2小时

#### NFS 文件存储
**结构**:
```
/nfs5/medlabel/medlabel_212/projects/
├── {projectId}/
│   ├── {imageName}/
│   │   ├── deepzoom/          # 病理图像金字塔
│   │   ├── {channelId}/       # 多通道图像
│   │   └── converted/         # 转换后图像
```

## 🔄 组件交互流程

### 图像处理完整流程

```mermaid
sequenceDiagram
    participant Java as Java后端
    participant RMQ as RabbitMQ
    participant Worker as Celery Worker
    participant Redis as Redis
    participant NFS as NFS存储
    
    Java->>RMQ: 发送图像转换任务
    RMQ->>Worker: 分发任务到Worker
    Worker->>Worker: 解析任务参数
    Worker->>NFS: 读取原始图像
    Worker->>Worker: 执行图像处理
    Worker->>NFS: 保存处理结果
    Worker->>Redis: 更新任务状态
    Worker->>RMQ: 确认消息处理完成
```

### 错误处理流程

```mermaid
sequenceDiagram
    participant Worker as Celery Worker
    participant RMQ as RabbitMQ
    participant DLQ as 死信队列
    participant Log as 日志系统
    
    Worker->>Worker: 处理任务异常
    Worker->>Log: 记录错误日志
    Worker->>RMQ: 确认消息(避免重复)
    RMQ->>DLQ: 移动到死信队列
    Worker->>Worker: 继续处理下一个任务
```

## 📊 性能特性

### 并发处理
- **多线程**: 5个并发消费者线程
- **多进程**: Celery 支持多进程并行
- **GPU 加速**: 自动选择最优 GPU

### 内存优化
- **流式处理**: 大图像分块处理
- **内存回收**: 及时释放图像内存
- **缓存策略**: 合理的缓存机制

### 容错机制
- **重试机制**: 自动重试失败任务
- **超时控制**: 软超时30分钟，硬超时40分钟
- **错误隔离**: 单个任务失败不影响其他任务

## 🔧 扩展指南

### 添加新的图像格式
1. 在 `image_process.py` 中添加新的处理函数
2. 在消息路由中添加新的 `imageTypeId`
3. 实现相应的格式转换逻辑

### 性能优化
1. 利用 GPU 加速计算密集型操作
2. 使用多进程处理 CPU 密集型任务
3. 优化内存使用和 I/O 操作

### 监控和调试
1. 使用 Timer 类进行性能监控
2. 配置详细的日志输出
3. 监控 GPU 和内存使用情况

---

*最后更新: 2025-07-03*
*文档版本: v1.0*
