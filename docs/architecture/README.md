# aiLabel_python_backend 架构文档

## 📁 文档目录

本目录包含 aiLabel_python_backend 项目的详细架构文档，帮助开发者深入理解系统设计和实现细节。

### 📋 文档列表

| 文档 | 描述 | 更新时间 |
|------|------|----------|
| [module-dependencies.md](./module-dependencies.md) | 模块依赖关系分析 | 2025-07-03 |

## 🏗️ 系统架构概览

aiLabel_python_backend 是一个专业的医学图像标注平台后端服务，采用分布式微服务架构，专注于处理各种医学图像格式的转换、预处理和标注任务。

### 核心特性

- 🔄 **多格式支持**: 支持 PNG/JPG、DICOM、病理图像(SVS/KFB)、多通道图像(QPTIFF)
- 🎨 **专业处理**: 提供医学图像专用的预处理算法
- 📊 **分布式架构**: 基于 Celery 的分布式任务队列系统
- 🔍 **实时监控**: 完善的任务进度跟踪和状态监控
- 🛡️ **高可用性**: 支持多实例部署和故障恢复
- 📈 **性能优化**: 针对大型医学图像的性能优化

### 技术架构

```mermaid
graph TB
    subgraph "客户端层"
        Java[Java后端]
        Web[Web前端]
    end

    subgraph "消息队列层"
        RabbitMQ[RabbitMQ集群<br/>消息中间件]
        Redis[Redis<br/>结果存储]
    end

    subgraph "处理层"
        Celery[Celery Worker集群<br/>图像处理服务]
    end

    subgraph "存储层"
        NFS[NFS存储<br/>文件系统]
    end

    Java --> RabbitMQ
    Web --> Java
    RabbitMQ --> Celery
    Celery --> Redis
    Celery --> NFS

    style Java fill:#e1f5fe
    style Celery fill:#f3e5f5
    style RabbitMQ fill:#fff3e0
    style Redis fill:#ffebee
    style NFS fill:#e8f5e8
```

### 核心组件

#### 1. **消息队列系统**
- **RabbitMQ**: 作为消息中间件，处理图像转换任务的分发
- **队列**: `medlabel_image_convert_queue` 专用队列
- **路由**: 支持任务类型路由和优先级处理

#### 2. **分布式处理**
- **Celery Workers**: 多进程并行处理图像转换任务
- **任务调度**: 支持任务优先级和资源分配
- **故障恢复**: 自动重试和错误处理机制

#### 3. **图像处理引擎**
- **多格式支持**: 统一的图像格式转换接口
- **专业算法**: 医学图像专用的处理算法
- **性能优化**: GPU 加速和内存优化

#### 4. **存储系统**
- **NFS**: 网络文件系统，存储原始和处理后的图像
- **Redis**: 缓存任务结果和状态信息
- **分层存储**: 支持热数据和冷数据分离

## 🔧 开发指南

### 环境要求

- **Python**: 3.11+
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **内存**: 8GB+ (推荐 16GB+)
- **存储**: 100GB+ 可用空间
- **GPU**: 可选，用于加速处理

### 依赖服务

- **RabbitMQ**: 3.8+
- **Redis**: 6.0+
- **NFS**: 网络文件系统

### 快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd aiLabel_python_backend

# 2. 创建环境
conda create -n aiLabel python=3.11
conda activate aiLabel

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env.dev
# 编辑 .env.dev 文件

# 5. 启动服务
./start_celery_dev.sh
```

## 📚 相关文档

### 项目文档
- [README.md](../../README.md) - 项目总览和快速开始
- [requirements.txt](../../requirements.txt) - 依赖列表

### 配置文档
- [config.py](../../config.py) - 配置文件
- [.env.example](../../.env.example) - 环境变量模板

### 运维文档
- [start_celery_*.sh](../../start_celery_dev.sh) - 启动脚本
- [stop_celery_*.sh](../../stop_celery_dev.sh) - 停止脚本

### 测试文档
- [testing-guide.md](../testing-guide.md) - 测试指南
- [test_*.py](../../test_queue.py) - 测试脚本

## 🤝 贡献指南

### 代码规范
- 遵循 PEP 8 Python 代码规范
- 使用 Black 进行代码格式化
- 使用 Flake8 进行代码检查

### 提交规范
- 使用清晰的提交信息
- 每个提交只包含一个功能或修复
- 添加适当的测试用例

### 文档更新
- 更新相关的架构文档
- 保持文档与代码同步
- 添加必要的示例和说明

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- **Issue**: 在项目仓库中创建 Issue
- **邮件**: 发送邮件至项目维护者
- **文档**: 查看项目 Wiki 或文档

---

*最后更新: 2025-07-03*
*维护者: aiLabel 开发团队*
