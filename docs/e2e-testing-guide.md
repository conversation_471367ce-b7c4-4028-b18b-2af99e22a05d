# 端到端测试指南

## 📋 测试概述

本指南提供了完整的端到端测试流程，用于验证重构后的图像处理系统是否正常工作。

## 🔧 测试环境配置

### 环境要求
- **开发环境配置**: `.env.dev`
- **RabbitMQ服务器**: `**************:25675`
- **Redis服务器**: `**************:26380`
- **NFS存储**: `/nfs5/medlabel/medlabel_dev_yjb/projects`

### 环境变量设置
```bash
export CELERY_ENV=dev
```

## 🚀 测试脚本说明

### 1. 完整端到端测试脚本
**文件**: `tests/e2e_test_pathology_processing.py`

**功能**:
- 自动化完整的端到端测试流程
- 测试病理图像处理功能 (imageTypeId=3)
- 验证所有组件的集成工作

**使用方法**:
```bash
cd /home/<USER>/vipa/aiLabel_python_backend
python tests/e2e_test_pathology_processing.py
```

**测试步骤**:
1. ✅ 建立RabbitMQ和Redis连接
2. ✅ 检查源文件是否存在
3. ✅ 清理之前的测试数据
4. ✅ 发送测试消息到队列
5. ✅ 监控Redis任务进度
6. ✅ 检查输出文件生成

### 2. 独立消息发送脚本
**文件**: `tests/send_test_message.py`

**功能**:
- 独立发送测试消息到RabbitMQ队列
- 支持多种图像类型测试
- 交互式选择测试类型

**使用方法**:
```bash
python tests/send_test_message.py
```

**支持的消息类型**:
- 病理图像处理 (imageTypeId=3)
- 普通图像处理 (imageTypeId=1)

### 3. Redis进度监控脚本
**文件**: `tests/monitor_redis_progress.py`

**功能**:
- 实时监控任务进度
- 查看所有活跃任务
- 清理测试数据

**使用方法**:
```bash
# 交互式监控
python tests/monitor_redis_progress.py

# 直接监控指定任务
python tests/monitor_redis_progress.py medlabel_image_convert_174
```

## 📨 测试消息格式

### 病理图像处理消息
```json
{
  "taskId": "medlabel_image_convert_174",
  "projectId": 13,
  "imageName": "3a149882-3294-4ebb-adc0-6fad764f2cd5",
  "imageTypeId": 3,
  "imageId": 174,
  "imageUrl": "/medical-data/data/liver/WSI/ZheEr/浙二/2022-10-18/201137807.svs"
}
```

### 普通图像处理消息
```json
{
  "taskId": "test_normal_image_001",
  "projectId": 999,
  "imageName": "test_normal_image",
  "imageTypeId": 1,
  "imageId": 999,
  "imageUrl": "/tmp/test_image.png"
}
```

## 🔍 验证点说明

### 1. 连接验证
- ✅ RabbitMQ连接成功
- ✅ Redis连接成功
- ✅ 队列声明成功

### 2. 消息处理验证
- ✅ 消息成功发送到队列
- ✅ 应用程序接收并解析消息
- ✅ 正确的图像处理器被调用

### 3. 任务执行验证
- ✅ Celery任务正确启动
- ✅ Redis进度更新正常
- ✅ 错误处理机制正常

### 4. 输出验证
- ✅ 输出目录结构正确
- ✅ 元数据文件生成
- ✅ 深度缩放瓦片生成

**预期输出目录结构**:
```
/nfs5/medlabel/medlabel_dev_yjb/projects/13/3a149882-3294-4ebb-adc0-6fad764f2cd5/deepzoom/
├── metadata.xml
└── imgs/
    ├── 0/
    │   └── 0_0.jpeg
    ├── 1/
    │   ├── 0_0.jpeg
    │   └── 0_1.jpeg
    └── ...
```

## 🏃‍♂️ 快速测试流程

### 方案一：完整自动化测试
```bash
# 1. 进入项目目录
cd /home/<USER>/vipa/aiLabel_python_backend

# 2. 确保应用程序正在运行
# (在另一个终端启动应用程序)

# 3. 运行完整测试
python tests/e2e_test_pathology_processing.py
```

### 方案二：分步手动测试
```bash
# 1. 发送测试消息
python tests/send_test_message.py

# 2. 监控进度 (在另一个终端)
python tests/monitor_redis_progress.py

# 3. 检查日志输出
tail -f app.log

# 4. 检查输出文件
ls -la /nfs5/medlabel/medlabel_dev_yjb/projects/13/3a149882-3294-4ebb-adc0-6fad764f2cd5/deepzoom/
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   ```
   ❌ RabbitMQ连接失败
   ```
   - 检查RabbitMQ服务是否运行
   - 验证网络连接和端口
   - 确认用户名密码正确

2. **源文件不存在**
   ```
   ⚠️ 源文件不存在: /medical-data/data/liver/WSI/ZheEr/浙二/2022-10-18/201137807.svs
   ```
   - 这是正常的，测试会验证错误处理逻辑
   - 如需测试实际文件处理，请提供有效的文件路径

3. **Redis进度未更新**
   ```
   ⚠️ 在60秒内未检测到Redis进度更新
   ```
   - 检查应用程序是否正在运行
   - 确认消息是否被正确消费
   - 查看应用程序日志

4. **输出文件未生成**
   ```
   ⚠️ 输出目录不存在
   ```
   - 检查NFS挂载是否正常
   - 确认目录权限
   - 验证任务是否成功执行

### 日志查看
```bash
# 查看应用程序日志
tail -f app.log

# 查看测试日志
tail -f e2e_test.log

# 查看Celery日志
tail -f logdev/celery.log
```

## 📊 测试报告

测试完成后，会生成详细的测试报告，包括：
- 各个测试步骤的执行结果
- 错误信息和调试信息
- 性能指标和时间统计
- 建议的后续操作

## 🎯 成功标准

测试被认为成功的标准：
- ✅ 所有连接建立成功
- ✅ 消息成功发送和接收
- ✅ 任务进度正常更新
- ✅ 错误处理机制正常工作
- ✅ 日志输出清晰有用

即使源文件不存在，只要错误处理正确，测试仍然被认为是成功的。
