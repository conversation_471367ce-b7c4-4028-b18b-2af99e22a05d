#!/bin/bash

# 标注平台开发环境Celery停止脚本
# Development Environment Celery Stop Script for aiLabel Platform

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [DEV] INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [DEV] SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [DEV] WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [DEV] ERROR: $1${NC}"
}

log_info "开始停止标注平台开发环境Celery服务..."

# 环境配置
ENV_TYPE="dev"
LOG_DIR="log/dev"
CONDA_ENV="/home/<USER>/miniconda3/envs/ailabel_env"

log_info "环境类型: 开发环境 ($ENV_TYPE)"
log_info "日志目录: $LOG_DIR"

# 1. 从PID文件停止进程
if [ -f "$LOG_DIR/celery.pid" ]; then
    celery_pid=$(cat "$LOG_DIR/celery.pid")
    log_info "从PID文件读取到进程ID: $celery_pid"
    
    if kill -0 "$celery_pid" 2>/dev/null; then
        log_info "正在停止开发环境Celery进程 (PID: $celery_pid)..."
        kill -TERM "$celery_pid" 2>/dev/null || true
        
        # 等待进程优雅退出
        for i in {1..10}; do
            if ! kill -0 "$celery_pid" 2>/dev/null; then
                log_success "开发环境Celery进程已优雅退出"
                break
            fi
            log_info "等待进程退出... ($i/10)"
            sleep 1
        done
        
        # 如果进程仍然存在，强制终止
        if kill -0 "$celery_pid" 2>/dev/null; then
            log_warning "进程未能优雅退出，强制终止..."
            kill -KILL "$celery_pid" 2>/dev/null || true
            sleep 2
            
            if kill -0 "$celery_pid" 2>/dev/null; then
                log_error "无法终止进程 $celery_pid"
            else
                log_success "已强制终止开发环境Celery进程"
            fi
        fi
    else
        log_warning "PID文件中的进程不存在，可能已经停止"
    fi
    
    # 添加会话结束标记到日志文件
    if [ -f "$LOG_DIR/celery.log" ]; then
        {
            echo ""
            echo "🛑 CELERY SESSION ENDED - $(date '+%Y-%m-%d %H:%M:%S')"
            echo "   Session terminated by: $(whoami)"
            echo "   Process ID: $celery_pid"
            echo "   Environment: 开发环境 (Development)"
            echo "================================================================================================"
            echo ""
        } >> "$LOG_DIR/celery.log"
    fi

    # 清理PID文件
    rm -f "$LOG_DIR/celery.pid"
    log_info "已清理PID文件"
else
    log_warning "未找到PID文件: $LOG_DIR/celery.pid"
fi

# 2. 强力查找并清理所有开发环境相关的celery进程
log_info "强力查找并清理所有开发环境Celery进程..."

current_user=$(whoami)
# 精确的进程识别模式，只针对标注平台开发环境
PROCESS_PATTERNS=(
    "celery.*ailabel_queue"                     # 基于队列名称（最精确）
    "aiLabel_dev_worker"                        # 基于worker名称
    "celery.*$CONDA_ENV.*aiLabel_python_backend.*dev"  # 基于项目路径和环境
)

log_info "当前用户: $current_user"
log_info "使用多种模式识别开发环境Celery进程..."

# 函数：强力清理进程
force_cleanup_processes() {
    local pattern="$1"
    local description="$2"

    log_info "检查模式: $pattern ($description)"

    # 使用pgrep查找进程
    local pids=$(pgrep -f "$pattern" 2>/dev/null || true)
    if [ -n "$pids" ]; then
        log_warning "发现匹配进程: $pids"
        for pid in $pids; do
            local proc_user=$(ps -o user= -p $pid 2>/dev/null || echo "")
            if [ "$proc_user" = "$current_user" ]; then
                log_info "终止进程 PID: $pid (用户: $proc_user)"
                # 先尝试优雅终止
                kill -TERM $pid 2>/dev/null || true
            fi
        done

        # 等待进程退出
        sleep 2

        # 检查是否还有残留，强制终止
        local remaining=$(pgrep -f "$pattern" 2>/dev/null || true)
        if [ -n "$remaining" ]; then
            log_warning "强制终止残留进程: $remaining"
            for pid in $remaining; do
                local proc_user=$(ps -o user= -p $pid 2>/dev/null || echo "")
                if [ "$proc_user" = "$current_user" ]; then
                    kill -KILL $pid 2>/dev/null || true
                fi
            done
        fi
    fi
}

# 逐个使用不同模式清理进程
for i in "${!PROCESS_PATTERNS[@]}"; do
    force_cleanup_processes "${PROCESS_PATTERNS[$i]}" "模式$((i+1))"
done

# 使用pkill进行额外清理（只针对标注平台开发环境）
log_info "使用pkill进行额外清理..."
pkill -f "celery.*ailabel_queue" 2>/dev/null || true
pkill -f "aiLabel_dev_worker" 2>/dev/null || true

# 等待所有终止操作完成
sleep 3

# 最终验证清理结果
log_info "验证清理结果..."
final_check_patterns=(
    "celery.*ailabel_queue"
    "aiLabel_dev_worker"
)

all_clean=true
for pattern in "${final_check_patterns[@]}"; do
    remaining=$(pgrep -f "$pattern" 2>/dev/null || true)
    if [ -n "$remaining" ]; then
        log_warning "仍有残留进程匹配模式 '$pattern': $remaining"
        all_clean=false
        # 显示详细信息
        ps aux | grep "$pattern" | grep -v grep || true
    fi
done

if [ "$all_clean" = true ]; then
    log_success "所有开发环境Celery进程已完全清理"
else
    log_warning "部分进程可能仍在运行，但已尽力清理"
fi

# 清理可能的临时文件
log_info "清理相关资源文件..."
temp_files=(
    "/tmp/celery-*-dev-*"
    "/tmp/ailabel-dev-*"
    "/var/run/celery-dev-*"
)

for temp_pattern in "${temp_files[@]}"; do
    if ls $temp_pattern 2>/dev/null | head -1 >/dev/null; then
        rm -f $temp_pattern 2>/dev/null || true
        log_info "已清理临时文件: $temp_pattern"
    fi
done

# 3. 显示系统中其他Celery进程（不影响它们）
other_celery=$(pgrep -f 'celery worker' 2>/dev/null || true)
if [ -n "$other_celery" ]; then
    log_info "系统中仍有其他Celery进程在运行（生产环境或其他平台）:"
    # 过滤掉标注平台开发环境的进程
    ps aux | grep 'celery worker' | grep -v grep | grep -v "ailabel_queue" | grep -v "aiLabel_dev_worker" || true
    log_info "这些进程不受影响，继续运行"
fi

log_success "标注平台开发环境Celery停止脚本执行完成!"
