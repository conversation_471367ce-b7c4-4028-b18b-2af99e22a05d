#!/bin/bash

# 纯 Celery 停止脚本 (无 Flask 依赖)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
LOG_DIR="./log"
PID_FILE="$LOG_DIR/celery.pid"

echo -e "${BLUE}🛑 停止纯 Celery 图像处理服务${NC}"
echo -e "${BLUE}时间: $(date)${NC}"

# 检查 PID 文件
if [[ ! -f "$PID_FILE" ]]; then
    echo -e "${YELLOW}⚠️ 未找到 PID 文件: $PID_FILE${NC}"
    echo -e "${YELLOW}尝试查找运行中的 Celery 进程...${NC}"
    
    # 查找 Celery 进程
    CELERY_PIDS=$(pgrep -f "celery.*worker" || true)
    if [[ -z "$CELERY_PIDS" ]]; then
        echo -e "${GREEN}✅ 没有运行中的 Celery 进程${NC}"
        exit 0
    else
        echo -e "${YELLOW}找到 Celery 进程: $CELERY_PIDS${NC}"
        for pid in $CELERY_PIDS; do
            echo -e "${YELLOW}停止进程 $pid${NC}"
            kill -TERM $pid
        done
        sleep 3
        # 强制杀死仍在运行的进程
        for pid in $CELERY_PIDS; do
            if ps -p $pid > /dev/null 2>&1; then
                echo -e "${RED}强制停止进程 $pid${NC}"
                kill -KILL $pid
            fi
        done
        echo -e "${GREEN}✅ 所有 Celery 进程已停止${NC}"
        exit 0
    fi
fi

# 读取 PID
CELERY_PID=$(cat "$PID_FILE")
echo -e "${YELLOW}📋 读取到 PID: $CELERY_PID${NC}"

# 检查进程是否存在
if ! ps -p $CELERY_PID > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ 进程 $CELERY_PID 不存在${NC}"
    rm -f "$PID_FILE"
    echo -e "${GREEN}✅ 清理完成${NC}"
    exit 0
fi

# 优雅停止
echo -e "${YELLOW}🛑 发送 TERM 信号停止进程...${NC}"
kill -TERM $CELERY_PID

# 等待进程停止
echo -e "${YELLOW}⏳ 等待进程停止...${NC}"
for i in {1..10}; do
    if ! ps -p $CELERY_PID > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 进程已优雅停止${NC}"
        rm -f "$PID_FILE"
        exit 0
    fi
    sleep 1
done

# 强制停止
echo -e "${RED}⚠️ 进程未响应，强制停止...${NC}"
kill -KILL $CELERY_PID

# 再次检查
sleep 2
if ps -p $CELERY_PID > /dev/null 2>&1; then
    echo -e "${RED}❌ 无法停止进程 $CELERY_PID${NC}"
    exit 1
else
    echo -e "${GREEN}✅ 进程已强制停止${NC}"
    rm -f "$PID_FILE"
fi

echo -e "${GREEN}🏁 停止完成${NC}"
