#!/bin/bash

# aiLabel 开发环境 Celery 停止脚本（简化版）
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[$(date '+%H:%M:%S')] $1${NC}"; }
log_success() { echo -e "${GREEN}[$(date '+%H:%M:%S')] $1${NC}"; }
log_error() { echo -e "${RED}[$(date '+%H:%M:%S')] $1${NC}"; }

# 配置
LOG_DIR=logdev
PID_FILE=$LOG_DIR/celery.pid

log_info "停止 aiLabel 开发环境 Celery 服务"

# 从PID文件停止
if [ -f "$PID_FILE" ]; then
    celery_pid=$(cat "$PID_FILE")
    log_info "停止进程 PID: $celery_pid"
    
    if kill -0 $celery_pid 2>/dev/null; then
        kill -TERM $celery_pid
        sleep 3
        
        # 检查是否还在运行
        if kill -0 $celery_pid 2>/dev/null; then
            log_info "强制终止进程"
            kill -KILL $celery_pid
        fi
        
        log_success "进程已停止"
    else
        log_info "进程已不存在"
    fi
    
    rm -f "$PID_FILE"
else
    log_info "未找到PID文件"
fi

# 清理残留进程
log_info "清理残留进程..."
pkill -f "celery.*ailabel_queue" 2>/dev/null || true
pkill -f "aiLabel.*worker" 2>/dev/null || true

log_success "Celery 服务已停止"
