#!/bin/bash

# 纯 Celery 启动脚本 (无 Flask 依赖)
# 专用于图像处理任务的消息队列处理

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
CONDA_ENV="myenv"
CELERY_ENV=${CELERY_ENV:-prod}
LOG_DIR="./log"
LOG_FILE="$LOG_DIR/celery.log"
PID_FILE="$LOG_DIR/celery.pid"

echo -e "${BLUE}🚀 启动纯 Celery 图像处理服务${NC}"
echo -e "${BLUE}环境: $CELERY_ENV${NC}"
echo -e "${BLUE}时间: $(date)${NC}"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 激活 conda 环境
echo -e "${YELLOW}📦 激活 conda 环境: $CONDA_ENV${NC}"
source ~/miniconda3/etc/profile.d/conda.sh
conda activate $CONDA_ENV

# 检查环境
if [[ "$CONDA_DEFAULT_ENV" != "$CONDA_ENV" ]]; then
    echo -e "${RED}❌ 无法激活 conda 环境: $CONDA_ENV${NC}"
    exit 1
fi

# 设置环境变量
export CELERY_ENV=$CELERY_ENV
echo -e "${GREEN}✅ 环境变量设置完成${NC}"

# 清理旧进程
echo -e "${YELLOW}🧹 清理旧的 Celery 进程${NC}"
if [[ -f "$PID_FILE" ]]; then
    OLD_PID=$(cat "$PID_FILE")
    if ps -p $OLD_PID > /dev/null 2>&1; then
        echo -e "${YELLOW}停止旧进程 (PID: $OLD_PID)${NC}"
        kill -TERM $OLD_PID
        sleep 3
        if ps -p $OLD_PID > /dev/null 2>&1; then
            kill -KILL $OLD_PID
        fi
    fi
    rm -f "$PID_FILE"
fi

# 备份旧日志
if [[ -f "$LOG_FILE" ]]; then
    BACKUP_LOG="$LOG_DIR/celery_$(date +%Y%m%d_%H%M%S).log"
    mv "$LOG_FILE" "$BACKUP_LOG"
    echo -e "${GREEN}📋 旧日志已备份到: $BACKUP_LOG${NC}"
fi

# 启动 Celery Worker
echo -e "${GREEN}🎯 启动 Celery Worker (纯消息队列模式)${NC}"
nohup celery -A app.celery worker \
    --loglevel=INFO \
    --queues=ailabel_queue \
    --concurrency=10 \
    --pool=prefork \
    --time-limit=2400 \
    --soft-time-limit=1800 \
    --max-tasks-per-child=50 \
    --prefetch-multiplier=1 \
    > "$LOG_FILE" 2>&1 &

# 保存 PID
CELERY_PID=$!
echo $CELERY_PID > "$PID_FILE"

echo -e "${GREEN}✅ Celery Worker 已启动${NC}"
echo -e "${GREEN}📋 PID: $CELERY_PID${NC}"
echo -e "${GREEN}📄 日志文件: $LOG_FILE${NC}"

# 等待启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 5

# 检查进程状态
if ps -p $CELERY_PID > /dev/null; then
    echo -e "${GREEN}🎉 Celery Worker 启动成功！${NC}"
    echo -e "${BLUE}📊 查看实时日志: tail -f $LOG_FILE${NC}"
    echo -e "${BLUE}🛑 停止服务: ./stop_celery_pure.sh${NC}"
else
    echo -e "${RED}❌ Celery Worker 启动失败${NC}"
    echo -e "${RED}📋 请检查日志: cat $LOG_FILE${NC}"
    exit 1
fi

echo -e "${GREEN}🏁 启动完成${NC}"
