#!/bin/bash

# aiLabel 开发环境 Celery 启动脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[$(date '+%H:%M:%S')] $1${NC}"; }
log_success() { echo -e "${GREEN}[$(date '+%H:%M:%S')] $1${NC}"; }
log_error() { echo -e "${RED}[$(date '+%H:%M:%S')] $1${NC}"; }

# 配置
CONDA_ENV=/home/<USER>/miniconda3/envs/ailabel_env
LOG_DIR=logdev

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
ENV_FILE="$PROJECT_ROOT/.env.dev"

log_info "启动 aiLabel 开发环境 Celery 服务"
log_info "项目根目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查环境配置
if [ ! -f "$ENV_FILE" ]; then
    log_error "配置文件 $ENV_FILE 不存在"
    exit 1
fi

# 激活conda环境
log_info "激活conda环境..."
source ~/miniconda3/etc/profile.d/conda.sh
conda activate $CONDA_ENV || { log_error "激活conda环境失败"; exit 1; }
log_success "conda环境激活成功"

# 创建日志目录
mkdir -p $LOG_DIR
log_info "日志目录: $LOG_DIR"

# 清理已存在的进程
log_info "清理已存在的Celery进程..."
pkill -f "celery.*ailabel_queue" 2>/dev/null || true
pkill -f "aiLabel.*worker" 2>/dev/null || true
sleep 2

# 清理PID文件
[ -f $LOG_DIR/celery.pid ] && rm -f $LOG_DIR/celery.pid
log_info "进程清理完成"

# 日志文件管理
log_info "管理日志文件..."
if [ -f $LOG_DIR/celery.log ]; then
    timestamp=$(date '+%Y%m%d_%H%M%S')
    mv $LOG_DIR/celery.log $LOG_DIR/celery_${timestamp}.log
    log_info "已备份旧日志文件"
fi

# 清理旧备份（保留最新5个）
ls -1t $LOG_DIR/celery_*.log 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true

# 创建新日志文件
{
    echo "🚀 Celery Session Started - $(date '+%Y-%m-%d %H:%M:%S')"
    echo "Environment: Development | Queue: ailabel_queue"
    echo "=============================================="
} > $LOG_DIR/celery.log

log_success "日志文件准备完成"

# 启动Celery Worker
log_info "启动Celery Worker..."
export CELERY_ENV=dev

nohup $CONDA_ENV/bin/celery -A app worker \
    --loglevel=DEBUG \
    --hostname=aiLabel_dev_worker@$(hostname) \
    --logfile=$LOG_DIR/celery.log \
    --pidfile=$LOG_DIR/celery.pid \
    --time-limit=2400 \
    --soft-time-limit=1800 \
    --concurrency=8 \
    --queues=ailabel_queue,medlabel_image_convert_queue \
    --pool=prefork \
    --max-tasks-per-child=30 \
    --include=tasks >> $LOG_DIR/celery.log 2>&1 &

# 验证启动
sleep 3
if [ -f $LOG_DIR/celery.pid ]; then
    celery_pid=$(cat $LOG_DIR/celery.pid)
    if kill -0 $celery_pid 2>/dev/null; then
        log_success "Celery Worker启动成功! PID: $celery_pid"
        echo "日志文件: tail -f $LOG_DIR/celery.log"
        echo "停止命令: ./stop_celery_dev.sh"
    else
        log_error "Celery Worker启动失败"
        exit 1
    fi
else
    log_error "未找到PID文件"
    exit 1
fi
