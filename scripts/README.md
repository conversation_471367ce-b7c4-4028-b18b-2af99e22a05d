# 脚本文件目录

这个目录包含了 aiLabel Python Backend 项目的所有启动和停止脚本。

## 📁 文件说明

### 开发环境脚本
- `start_celery_dev.sh` - 开发环境 Celery 启动脚本（重构简化版）
- `stop_celery_dev.sh` - 开发环境 Celery 停止脚本（原版）
- `stop_celery_dev_simple.sh` - 开发环境 Celery 停止脚本（简化版）

### 生产环境脚本
- `start_celery_prod.sh` - 生产环境 Celery 启动脚本
- `stop_celery_prod.sh` - 生产环境 Celery 停止脚本

### 通用脚本
- `start_celery.sh` - 通用 Celery 启动脚本
- `stop_celery.sh` - 通用 Celery 停止脚本
- `start_celery_pure.sh` - 纯 Celery 启动脚本
- `stop_celery_pure.sh` - 纯 Celery 停止脚本

## 🚀 推荐使用方法

### 开发环境（推荐）
```bash
# 从项目根目录启动
./start.sh

# 停止服务
./stop.sh
```

### 直接使用脚本
```bash
# 启动开发环境
./scripts/start_celery_dev.sh

# 停止服务（简化版，推荐）
./scripts/stop_celery_dev_simple.sh

# 停止服务（原版）
./scripts/stop_celery_dev.sh
```

## 📝 脚本特点

### 重构后的优势
- **简化版启动脚本**：从 320 行精简到 101 行，启动更快
- **简化版停止脚本**：新增的 `stop_celery_dev_simple.sh`，操作更可靠
- **便捷启动**：根目录的 `start.sh` 和 `stop.sh` 提供快速访问

### 功能保留
- 完整的环境检查和配置
- 进程管理和清理
- 日志文件管理
- 错误处理和状态验证

## ⚠️ 注意事项

- 确保 conda 环境 `ailabel_env` 已正确配置
- 检查 `.env.dev` 或 `.env.prod` 配置文件存在
- 脚本需要执行权限（已自动设置）
