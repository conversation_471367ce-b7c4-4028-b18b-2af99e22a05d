# 测试文件目录

这个目录包含了 aiLabel Python Backend 项目的所有测试文件。

## 📁 文件说明

### 连接测试
- `test_redis_connection.py` - Redis 连接测试
- `test_rabbitmq_connection.py` - RabbitMQ 连接测试  
- `test_rabbitmq_25674.py` - RabbitMQ 特定端口连接测试
- `test_connection.py` - 通用连接测试

### 队列测试
- `test_queue.py` - 消息队列功能测试

### 配置测试
- `setup_ssh.py` - SSH 连接配置脚本

## 🚀 使用方法

### 测试 Redis 连接
```bash
cd tests
python test_redis_connection.py
```

### 测试 RabbitMQ 连接
```bash
cd tests
python test_rabbitmq_connection.py
```

### 测试消息队列
```bash
cd tests
python test_queue.py
```

## 📝 注意事项

- 运行测试前请确保相关服务（Redis、RabbitMQ）正在运行
- 测试脚本会使用项目根目录的配置文件（.env.dev 或 .env.prod）
- 如果测试失败，请检查网络连接和服务配置
