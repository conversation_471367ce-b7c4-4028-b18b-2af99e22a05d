#!/usr/bin/env python3
"""
端到端测试脚本 - 病理图像处理系统
测试重构后的图像处理系统是否正常工作

使用方法:
    python tests/e2e_test_pathology_processing.py

环境要求:
    - 开发环境配置 (.env.dev)
    - RabbitMQ: 10.214.242.155:25675
    - Redis: 10.214.242.155:26380
    - NFS存储: /nfs5/medlabel/medlabel_dev_yjb/projects
"""

import os
import sys
import json
import time
import pika
import redis
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 设置环境变量使用开发环境配置
os.environ['CELERY_ENV'] = 'dev'

# 直接导入配置模块
import config as config_module
RABBITMQ_HOST = config_module.RABBITMQ_HOST
RABBITMQ_PORT = config_module.RABBITMQ_PORT
RABBITMQ_ACCOUNT = config_module.RABBITMQ_ACCOUNT
RABBITMQ_PASSWORD = config_module.RABBITMQ_PASSWORD
REDIS_HOST = config_module.REDIS_HOST
REDIS_PORT = config_module.REDIS_PORT
PROJECT_SAVE_DIR = config_module.PROJECT_SAVE_DIR

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('e2e_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class E2ETestRunner:
    """端到端测试运行器"""
    
    def __init__(self):
        self.test_message = {
            'taskId': 'medlabel_image_convert_175',
            'projectId': 13,
            'imageName': '**********',
            'imageTypeId': 3,  # 病理图像类型
            'imageId': 175,
            'imageUrl': '/medical-data/data/liver/WSI/ZheEr/浙二/2022-10-18/*********.svs'
        }
        self.queue_name = 'medlabel_image_convert_queue'
        self.expected_output_dir = os.path.join(
            PROJECT_SAVE_DIR, 
            str(self.test_message['projectId']),
            self.test_message['imageName'],
            'deepzoom'
        )
        self.redis_client = None
        self.rabbitmq_connection = None
        
    def setup_connections(self):
        """建立连接"""
        try:
            # Redis连接
            self.redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
            self.redis_client.ping()
            logger.info(f"✅ Redis连接成功: {REDIS_HOST}:{REDIS_PORT}")
            
            # RabbitMQ连接
            credentials = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_HOST,
                port=RABBITMQ_PORT,
                virtual_host='/',
                credentials=credentials,
                heartbeat=300,
                blocked_connection_timeout=300
            )
            self.rabbitmq_connection = pika.BlockingConnection(parameters)
            logger.info(f"✅ RabbitMQ连接成功: {RABBITMQ_HOST}:{RABBITMQ_PORT}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 连接建立失败: {e}")
            return False
    
    def check_source_file(self):
        """检查源文件是否存在"""
        source_file = self.test_message['imageUrl']
        if os.path.exists(source_file):
            file_size = os.path.getsize(source_file)
            logger.info(f"✅ 源文件存在: {source_file} (大小: {file_size:,} bytes)")
            return True
        else:
            logger.warning(f"⚠️ 源文件不存在: {source_file}")
            logger.info("📝 将测试错误处理逻辑")
            return False
    
    def send_test_message(self):
        """发送测试消息到RabbitMQ队列"""
        try:
            channel = self.rabbitmq_connection.channel()
            
            # 声明队列
            channel.queue_declare(
                queue=self.queue_name,
                durable=True,
                arguments={
                    'x-dead-letter-exchange': 'dlx.direct',
                    'x-dead-letter-routing-key': 'image_convert.dlq'
                }
            )
            
            # 发送消息
            message_body = json.dumps(self.test_message)
            channel.basic_publish(
                exchange='',
                routing_key=self.queue_name,
                body=message_body,
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 消息持久化
                    timestamp=int(time.time())
                )
            )
            
            logger.info(f"✅ 测试消息已发送到队列: {self.queue_name}")
            logger.info(f"📨 消息内容: {json.dumps(self.test_message, indent=2)}")
            
            channel.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ 发送消息失败: {e}")
            return False
    
    def monitor_redis_progress(self, timeout=300):
        """监控Redis中的任务进度"""
        logger.info("🔍 开始监控Redis任务进度...")
        
        task_id = self.test_message['taskId']
        progress_key = f'image_convert_task_progress:{task_id}'
        processed_key = f'image_convert_task_processed:{task_id}'
        
        start_time = time.time()
        last_progress = None
        
        while time.time() - start_time < timeout:
            try:
                # 检查进度
                progress = self.redis_client.get(progress_key)
                processed = self.redis_client.get(processed_key)
                
                current_status = {
                    'progress': progress,
                    'processed': processed,
                    'elapsed': f"{time.time() - start_time:.1f}s"
                }
                
                if current_status != last_progress:
                    logger.info(f"📊 任务进度: {current_status}")
                    last_progress = current_status
                
                # 检查是否有进度更新
                if progress is not None or processed is not None:
                    logger.info("✅ 检测到Redis中的任务进度更新")
                    return True
                
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ Redis监控错误: {e}")
                time.sleep(5)
        
        logger.warning(f"⚠️ 在{timeout}秒内未检测到Redis进度更新")
        return False
    
    def check_output_files(self):
        """检查输出文件是否生成"""
        logger.info("🔍 检查输出文件...")
        
        output_dir = Path(self.expected_output_dir)
        logger.info(f"📁 预期输出目录: {output_dir}")
        
        if not output_dir.exists():
            logger.warning(f"⚠️ 输出目录不存在: {output_dir}")
            return False
        
        # 检查关键文件
        expected_files = [
            'metadata.xml',
            'imgs'
        ]
        
        found_files = []
        missing_files = []
        
        for file_name in expected_files:
            file_path = output_dir / file_name
            if file_path.exists():
                found_files.append(file_name)
                if file_name == 'imgs' and file_path.is_dir():
                    # 检查imgs目录下的层级结构
                    levels = list(file_path.glob('*'))
                    logger.info(f"📂 发现 {len(levels)} 个缩放层级")
                    for level_dir in sorted(levels)[:3]:  # 只显示前3个层级
                        if level_dir.is_dir():
                            tiles = list(level_dir.glob('*.jpeg'))
                            logger.info(f"   层级 {level_dir.name}: {len(tiles)} 个瓦片")
            else:
                missing_files.append(file_name)
        
        if found_files:
            logger.info(f"✅ 找到输出文件: {found_files}")
        
        if missing_files:
            logger.warning(f"⚠️ 缺少文件: {missing_files}")
        
        return len(found_files) > 0
    
    def cleanup_test_data(self):
        """清理测试数据"""
        try:
            task_id = self.test_message['taskId']
            
            # 清理Redis键
            keys_to_delete = [
                f'image_convert_task_progress:{task_id}',
                f'image_convert_task_processed:{task_id}'
            ]
            
            for key in keys_to_delete:
                if self.redis_client.exists(key):
                    self.redis_client.delete(key)
                    logger.info(f"🧹 已清理Redis键: {key}")
            
            logger.info("✅ 测试数据清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理测试数据失败: {e}")
    
    def run_test(self):
        """运行完整的端到端测试"""
        logger.info("🚀 开始端到端测试")
        logger.info(f"🔧 测试环境: 开发环境 (CELERY_ENV=dev)")
        logger.info(f"📍 RabbitMQ: {RABBITMQ_HOST}:{RABBITMQ_PORT}")
        logger.info(f"📍 Redis: {REDIS_HOST}:{REDIS_PORT}")
        logger.info(f"📍 存储目录: {PROJECT_SAVE_DIR}")
        
        test_results = {
            'connections': False,
            'source_file': False,
            'message_sent': False,
            'redis_progress': False,
            'output_files': False
        }
        
        try:
            # 1. 建立连接
            logger.info("\n" + "="*50)
            logger.info("步骤 1: 建立连接")
            test_results['connections'] = self.setup_connections()
            
            if not test_results['connections']:
                logger.error("❌ 连接建立失败，测试终止")
                return test_results
            
            # 2. 检查源文件
            logger.info("\n" + "="*50)
            logger.info("步骤 2: 检查源文件")
            test_results['source_file'] = self.check_source_file()
            
            # 3. 清理之前的测试数据
            logger.info("\n" + "="*50)
            logger.info("步骤 3: 清理之前的测试数据")
            self.cleanup_test_data()
            
            # 4. 发送测试消息
            logger.info("\n" + "="*50)
            logger.info("步骤 4: 发送测试消息")
            test_results['message_sent'] = self.send_test_message()
            
            if not test_results['message_sent']:
                logger.error("❌ 消息发送失败，测试终止")
                return test_results
            
            # 5. 监控Redis进度
            logger.info("\n" + "="*50)
            logger.info("步骤 5: 监控任务进度")
            test_results['redis_progress'] = self.monitor_redis_progress(timeout=60)
            
            # 6. 检查输出文件
            logger.info("\n" + "="*50)
            logger.info("步骤 6: 检查输出文件")
            # 等待一段时间让任务完成
            time.sleep(10)
            test_results['output_files'] = self.check_output_files()
            
            return test_results
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return test_results
        
        finally:
            # 关闭连接
            if self.rabbitmq_connection and not self.rabbitmq_connection.is_closed:
                self.rabbitmq_connection.close()
                logger.info("🔌 RabbitMQ连接已关闭")
    
    def print_test_summary(self, results):
        """打印测试总结"""
        logger.info("\n" + "="*60)
        logger.info("🎯 端到端测试总结")
        logger.info("="*60)
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name:20s}: {status}")
        
        logger.info("-" * 60)
        logger.info(f"总计: {passed_tests}/{total_tests} 个测试通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！重构后的系统工作正常")
        elif passed_tests >= total_tests - 1:
            logger.info("⚠️ 大部分测试通过，系统基本正常")
        else:
            logger.info("❌ 多个测试失败，需要检查系统配置")
        
        logger.info("="*60)


def main():
    """主函数"""
    print("🔬 图像处理系统端到端测试")
    print("=" * 50)
    
    # 创建测试运行器
    test_runner = E2ETestRunner()
    
    # 运行测试
    results = test_runner.run_test()
    
    # 打印总结
    test_runner.print_test_summary(results)
    
    # 返回退出码
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    if passed_tests == total_tests:
        sys.exit(0)  # 所有测试通过
    else:
        sys.exit(1)  # 有测试失败


if __name__ == "__main__":
    main()
