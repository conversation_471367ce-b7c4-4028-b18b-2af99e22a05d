#!/usr/bin/env python3
"""
独立的消息发送脚本
用于向RabbitMQ队列发送测试消息

使用方法:
    python tests/send_test_message.py
"""

import os
import sys
import json
import pika
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置环境变量使用开发环境配置
os.environ['CELERY_ENV'] = 'dev'

from config import RABBITMQ_HOST, RABBITMQ_PORT, RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD

def send_pathology_test_message():
    """发送病理图像处理测试消息"""
    
    # 测试消息
    test_message = {
        'taskId': 'medlabel_image_convert_174',
        'projectId': 13,
        'imageName': '3a149882-3294-4ebb-adc0-6fad764f2cd5',
        'imageTypeId': 3,  # 病理图像类型
        'imageId': 174,
        'imageUrl': '/medical-data/data/liver/WSI/ZheEr/浙二/2022-10-18/*********.svs'
    }
    
    queue_name = 'medlabel_image_convert_queue'
    
    try:
        print("🔗 连接到RabbitMQ...")
        print(f"   服务器: {RABBITMQ_HOST}:{RABBITMQ_PORT}")
        print(f"   用户: {RABBITMQ_ACCOUNT}")
        
        # 建立连接
        credentials = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
        parameters = pika.ConnectionParameters(
            host=RABBITMQ_HOST,
            port=RABBITMQ_PORT,
            virtual_host='/',
            credentials=credentials,
            heartbeat=300,
            blocked_connection_timeout=300
        )
        
        connection = pika.BlockingConnection(parameters)
        channel = connection.channel()
        
        print("✅ RabbitMQ连接成功")
        
        # 声明队列
        print(f"📋 声明队列: {queue_name}")
        channel.queue_declare(
            queue=queue_name,
            durable=True,
            arguments={
                'x-dead-letter-exchange': 'dlx.direct',
                'x-dead-letter-routing-key': 'image_convert.dlq'
            }
        )
        
        # 发送消息
        message_body = json.dumps(test_message, indent=2)
        print(f"📨 发送测试消息:")
        print(message_body)
        
        channel.basic_publish(
            exchange='',
            routing_key=queue_name,
            body=message_body,
            properties=pika.BasicProperties(
                delivery_mode=2,  # 消息持久化
                timestamp=int(time.time())
            )
        )
        
        print("✅ 消息发送成功!")
        print(f"⏰ 发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 关闭连接
        connection.close()
        print("🔌 连接已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 发送消息失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def send_normal_image_test_message():
    """发送普通图像处理测试消息"""
    
    # 测试消息 - 普通图像类型
    test_message = {
        'taskId': 'test_normal_image_001',
        'projectId': 999,
        'imageName': 'test_normal_image',
        'imageTypeId': 1,  # 普通图像类型
        'imageId': 999,
        'imageUrl': '/tmp/test_image.png'  # 使用一个简单的测试路径
    }
    
    queue_name = 'medlabel_image_convert_queue'
    
    try:
        print("🔗 连接到RabbitMQ...")
        
        credentials = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
        parameters = pika.ConnectionParameters(
            host=RABBITMQ_HOST,
            port=RABBITMQ_PORT,
            virtual_host='/',
            credentials=credentials
        )
        
        connection = pika.BlockingConnection(parameters)
        channel = connection.channel()
        
        print("✅ RabbitMQ连接成功")
        
        # 声明队列
        channel.queue_declare(queue=queue_name, durable=True)
        
        # 发送消息
        message_body = json.dumps(test_message, indent=2)
        print(f"📨 发送普通图像测试消息:")
        print(message_body)
        
        channel.basic_publish(
            exchange='',
            routing_key=queue_name,
            body=message_body,
            properties=pika.BasicProperties(delivery_mode=2)
        )
        
        print("✅ 普通图像测试消息发送成功!")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 发送普通图像测试消息失败: {e}")
        return False

def main():
    """主函数"""
    print("📨 RabbitMQ测试消息发送器")
    print("=" * 40)
    
    while True:
        print("\n请选择要发送的测试消息类型:")
        print("1. 病理图像处理消息 (imageTypeId=3)")
        print("2. 普通图像处理消息 (imageTypeId=1)")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            print("\n🔬 发送病理图像处理测试消息...")
            success = send_pathology_test_message()
            if success:
                print("🎉 病理图像测试消息发送完成!")
            else:
                print("❌ 病理图像测试消息发送失败!")
                
        elif choice == '2':
            print("\n🖼️ 发送普通图像处理测试消息...")
            success = send_normal_image_test_message()
            if success:
                print("🎉 普通图像测试消息发送完成!")
            else:
                print("❌ 普通图像测试消息发送失败!")
                
        elif choice == '3':
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
