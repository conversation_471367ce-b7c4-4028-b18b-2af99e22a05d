#!/usr/bin/env python3
"""
Redis任务进度监控脚本
实时监控图像处理任务的进度

使用方法:
    python tests/monitor_redis_progress.py [task_id]
"""

import os
import sys
import time
import redis
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置环境变量使用开发环境配置
os.environ['CELERY_ENV'] = 'dev'

from config import REDIS_HOST, REDIS_PORT

class RedisProgressMonitor:
    """Redis进度监控器"""
    
    def __init__(self):
        self.redis_client = None
        self.connect_redis()
    
    def connect_redis(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(
                host=REDIS_HOST, 
                port=REDIS_PORT, 
                decode_responses=True
            )
            self.redis_client.ping()
            print(f"✅ Redis连接成功: {REDIS_HOST}:{REDIS_PORT}")
            return True
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            return False
    
    def get_all_task_keys(self):
        """获取所有任务相关的Redis键"""
        try:
            # 查找所有图像转换任务相关的键
            progress_keys = self.redis_client.keys('image_convert_task_progress:*')
            processed_keys = self.redis_client.keys('image_convert_task_processed:*')
            
            # 提取任务ID
            task_ids = set()
            for key in progress_keys:
                task_id = key.replace('image_convert_task_progress:', '')
                task_ids.add(task_id)
            
            for key in processed_keys:
                task_id = key.replace('image_convert_task_processed:', '')
                task_ids.add(task_id)
            
            return list(task_ids)
            
        except Exception as e:
            print(f"❌ 获取任务键失败: {e}")
            return []
    
    def get_task_progress(self, task_id):
        """获取指定任务的进度信息"""
        try:
            progress_key = f'image_convert_task_progress:{task_id}'
            processed_key = f'image_convert_task_processed:{task_id}'
            
            progress = self.redis_client.get(progress_key)
            processed = self.redis_client.get(processed_key)
            
            # 获取键的TTL
            progress_ttl = self.redis_client.ttl(progress_key)
            processed_ttl = self.redis_client.ttl(processed_key)
            
            return {
                'task_id': task_id,
                'progress': progress,
                'processed': processed,
                'progress_ttl': progress_ttl if progress_ttl > 0 else None,
                'processed_ttl': processed_ttl if processed_ttl > 0 else None,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            }
            
        except Exception as e:
            print(f"❌ 获取任务进度失败: {e}")
            return None
    
    def monitor_specific_task(self, task_id, duration=300):
        """监控指定任务的进度"""
        print(f"🔍 开始监控任务: {task_id}")
        print(f"⏱️ 监控时长: {duration}秒")
        print("-" * 60)
        
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < duration:
            current_status = self.get_task_progress(task_id)
            
            if current_status and current_status != last_status:
                print(f"[{current_status['timestamp']}] 任务进度更新:")
                print(f"  📊 进度: {current_status['progress']}")
                print(f"  🔢 已处理: {current_status['processed']}")
                if current_status['progress_ttl']:
                    print(f"  ⏰ 进度键TTL: {current_status['progress_ttl']}秒")
                if current_status['processed_ttl']:
                    print(f"  ⏰ 处理键TTL: {current_status['processed_ttl']}秒")
                print("-" * 40)
                
                last_status = current_status
            
            time.sleep(2)
        
        print(f"⏰ 监控结束 (已监控 {duration} 秒)")
    
    def monitor_all_tasks(self, duration=60):
        """监控所有任务的进度"""
        print("🔍 监控所有图像处理任务")
        print(f"⏱️ 监控时长: {duration}秒")
        print("=" * 60)
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            task_ids = self.get_all_task_keys()
            
            if task_ids:
                print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 发现 {len(task_ids)} 个活跃任务:")
                
                for task_id in task_ids:
                    status = self.get_task_progress(task_id)
                    if status:
                        print(f"  📋 {task_id}:")
                        print(f"     进度: {status['progress']}")
                        print(f"     已处理: {status['processed']}")
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 未发现活跃任务")
            
            time.sleep(5)
        
        print(f"\n⏰ 监控结束")
    
    def list_all_tasks(self):
        """列出所有任务"""
        print("📋 当前Redis中的所有图像处理任务:")
        print("=" * 60)
        
        task_ids = self.get_all_task_keys()
        
        if not task_ids:
            print("📭 未发现任何任务")
            return
        
        for i, task_id in enumerate(task_ids, 1):
            status = self.get_task_progress(task_id)
            if status:
                print(f"{i}. 任务ID: {task_id}")
                print(f"   进度: {status['progress']}")
                print(f"   已处理: {status['processed']}")
                if status['progress_ttl']:
                    print(f"   TTL: {status['progress_ttl']}秒")
                print("-" * 40)
    
    def clear_task_data(self, task_id):
        """清理指定任务的Redis数据"""
        try:
            progress_key = f'image_convert_task_progress:{task_id}'
            processed_key = f'image_convert_task_processed:{task_id}'
            
            deleted_count = 0
            
            if self.redis_client.exists(progress_key):
                self.redis_client.delete(progress_key)
                deleted_count += 1
                print(f"🧹 已删除: {progress_key}")
            
            if self.redis_client.exists(processed_key):
                self.redis_client.delete(processed_key)
                deleted_count += 1
                print(f"🧹 已删除: {processed_key}")
            
            if deleted_count > 0:
                print(f"✅ 已清理任务 {task_id} 的 {deleted_count} 个Redis键")
            else:
                print(f"ℹ️ 任务 {task_id} 没有找到相关的Redis键")
                
        except Exception as e:
            print(f"❌ 清理任务数据失败: {e}")

def main():
    """主函数"""
    print("📊 Redis任务进度监控器")
    print("=" * 40)
    
    monitor = RedisProgressMonitor()
    
    if len(sys.argv) > 1:
        # 如果提供了任务ID，直接监控该任务
        task_id = sys.argv[1]
        monitor.monitor_specific_task(task_id)
        return
    
    while True:
        print("\n请选择操作:")
        print("1. 列出所有任务")
        print("2. 监控所有任务 (60秒)")
        print("3. 监控指定任务")
        print("4. 清理任务数据")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            monitor.list_all_tasks()
            
        elif choice == '2':
            monitor.monitor_all_tasks(60)
            
        elif choice == '3':
            task_id = input("请输入任务ID: ").strip()
            if task_id:
                duration = input("监控时长(秒，默认300): ").strip()
                duration = int(duration) if duration.isdigit() else 300
                monitor.monitor_specific_task(task_id, duration)
            else:
                print("❌ 任务ID不能为空")
                
        elif choice == '4':
            task_id = input("请输入要清理的任务ID: ").strip()
            if task_id:
                confirm = input(f"确认清理任务 {task_id} 的数据? (y/N): ").strip().lower()
                if confirm == 'y':
                    monitor.clear_task_data(task_id)
                else:
                    print("❌ 操作已取消")
            else:
                print("❌ 任务ID不能为空")
                
        elif choice == '5':
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
