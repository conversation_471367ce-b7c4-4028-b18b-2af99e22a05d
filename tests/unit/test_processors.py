"""
重构后模块的测试用例
测试各个模块的功能正确性和向后兼容性
"""
import unittest
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
import sys
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.connections import ConnectionManager
from app.utils import (
    set_permissions, time_logger, count_total_tile, 
    generate_metadata, ensure_directory_exists,
    validate_required_params
)
from app.image_processors import (
    BaseImageProcessor, NormalImageProcessor, 
    DicomImageProcessor, PathologyImageProcessor,
    MultichannelImageProcessor
)
from app.message_handler import MessageHandler
from app.image_process import image_process


class TestConnectionManager(unittest.TestCase):
    """测试连接管理器"""
    
    def setUp(self):
        self.connection_manager = ConnectionManager()
    
    def test_connection_manager_initialization(self):
        """测试连接管理器初始化"""
        self.assertIsNotNone(self.connection_manager.redis_client)
        self.assertEqual(
            self.connection_manager.image_task_finish_callback_queue,
            'medlabel_image_convert_task_finish_callback_queue'
        )
    
    def test_send_progress_update(self):
        """测试进度更新发送"""
        # 这个方法现在只是打印，不会抛出异常
        try:
            self.connection_manager.send_progress_update(
                "test_task", "test_image", 1, 0.5, "测试消息"
            )
        except Exception as e:
            self.fail(f"send_progress_update raised {e} unexpectedly!")


class TestUtils(unittest.TestCase):
    """测试工具函数"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_ensure_directory_exists(self):
        """测试目录创建"""
        test_dir = os.path.join(self.temp_dir, "test_subdir")
        ensure_directory_exists(test_dir)
        self.assertTrue(os.path.exists(test_dir))
    
    def test_validate_required_params(self):
        """测试参数验证"""
        self.assertTrue(validate_required_params("a", "b", "c"))
        self.assertFalse(validate_required_params("a", None, "c"))
        self.assertFalse(validate_required_params("a", "", "c"))
    
    def test_count_total_tile(self):
        """测试切片数量计算"""
        result = count_total_tile(3, 1024, 1024, 256)
        self.assertIsInstance(result, dict)
        self.assertGreater(len(result), 0)
    
    def test_generate_metadata(self):
        """测试元数据生成"""
        generate_metadata(1024, 1024, self.temp_dir, 256)
        metadata_file = os.path.join(self.temp_dir, "metadata.xml")
        self.assertTrue(os.path.exists(metadata_file))
        
        with open(metadata_file, 'r') as f:
            content = f.read()
            self.assertIn('TileSize="256"', content)
            self.assertIn('Width="1024"', content)
            self.assertIn('Height="1024"', content)
    
    def test_time_logger_decorator(self):
        """测试时间记录装饰器"""
        @time_logger
        def test_function():
            return "test_result"
        
        result = test_function()
        self.assertEqual(result, "test_result")


class TestImageProcessors(unittest.TestCase):
    """测试图像处理器"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.test_data = {
            'taskId': 'test_task_123',
            'projectId': 'test_project_456',
            'imageName': 'test_image',
            'imageId': 'test_image_789',
            'imageUrl': '/path/to/test/image.png'
        }
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_base_processor_initialization(self):
        """测试基础处理器初始化"""
        # 使用具体的处理器实例来测试基础功能
        processor = NormalImageProcessor()
        self.assertIsNotNone(processor.connection_manager)

    def test_base_processor_validate_params(self):
        """测试基础处理器参数验证"""
        # 使用具体的处理器实例来测试基础功能
        processor = NormalImageProcessor()
        self.assertTrue(processor.validate_common_params(self.test_data))

        invalid_data = self.test_data.copy()
        del invalid_data['taskId']
        self.assertFalse(processor.validate_common_params(invalid_data))
    
    def test_normal_image_processor_initialization(self):
        """测试普通图像处理器初始化"""
        processor = NormalImageProcessor()
        self.assertIsInstance(processor, BaseImageProcessor)
    
    def test_dicom_image_processor_initialization(self):
        """测试DICOM图像处理器初始化"""
        processor = DicomImageProcessor()
        self.assertIsInstance(processor, BaseImageProcessor)
        self.assertIsNotNone(processor.normal_processor)
    
    def test_dicom_normalize_pixel_value(self):
        """测试DICOM像素值标准化"""
        processor = DicomImageProcessor()
        
        # 测试边界值
        self.assertEqual(processor._normalize_pixel_value(-1000), 0)
        self.assertEqual(processor._normalize_pixel_value(2000), 255)
        
        # 测试中间值
        result = processor._normalize_pixel_value(1000)
        self.assertGreaterEqual(result, 0)
        self.assertLessEqual(result, 255)
    
    def test_pathology_image_processor_initialization(self):
        """测试病理图像处理器初始化"""
        processor = PathologyImageProcessor()
        self.assertIsInstance(processor, BaseImageProcessor)
    
    def test_multichannel_image_processor_initialization(self):
        """测试多通道图像处理器初始化"""
        processor = MultichannelImageProcessor()
        self.assertIsInstance(processor, BaseImageProcessor)


class TestMessageHandler(unittest.TestCase):
    """测试消息处理器"""
    
    def setUp(self):
        self.message_handler = MessageHandler()
        self.test_message_data = {
            'taskId': 'test_task_123',
            'projectId': 'test_project_456',
            'imageName': 'test_image',
            'imageId': 'test_image_789',
            'imageUrl': '/path/to/test/image.png',
            'imageTypeId': 1
        }
    
    def test_message_handler_initialization(self):
        """测试消息处理器初始化"""
        self.assertEqual(len(self.message_handler.processors), 4)
        self.assertIn(1, self.message_handler.processors)
        self.assertIn(2, self.message_handler.processors)
        self.assertIn(3, self.message_handler.processors)
        self.assertIn(4, self.message_handler.processors)
    
    def test_handle_image_process_message(self):
        """测试图像处理消息处理"""
        mock_body = Mock()
        mock_body.decode.return_value = json.dumps(self.test_message_data)

        # 模拟消息处理
        try:
            self.message_handler.handle_image_process_message(
                None, None, None, mock_body
            )
        except Exception as e:
            # 由于没有实际的文件路径，预期会有异常，但不应该是导入或初始化错误
            self.assertNotIn("import", str(e).lower())
            self.assertNotIn("module", str(e).lower())


class TestBackwardCompatibility(unittest.TestCase):
    """测试向后兼容性"""
    
    def test_main_entry_point_exists(self):
        """测试主入口点存在"""
        from app.image_process import image_process
        self.assertTrue(callable(image_process))
    
    def test_legacy_functions_exist(self):
        """测试遗留函数存在"""
        from app.image_process import (
            get_rabbitmq_connection, send_progress_update,
            thumbnail_convert, thumbnail_convert_for_single_image,
            get_slide, read_region, get_tile,
            normalization, dicom_convert,
            normal_image_process, dicom_image_process,
            patho_image_process, channel_image_process
        )
        
        # 检查所有函数都是可调用的
        functions = [
            get_rabbitmq_connection, send_progress_update,
            thumbnail_convert, thumbnail_convert_for_single_image,
            get_slide, read_region, get_tile,
            normalization, dicom_convert,
            normal_image_process, dicom_image_process,
            patho_image_process, channel_image_process
        ]
        
        for func in functions:
            self.assertTrue(callable(func), f"{func.__name__} is not callable")


if __name__ == '__main__':
    unittest.main()
