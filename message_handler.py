#!/usr/bin/env python3
"""
消息处理器 - 处理原始JSON消息
"""
import json
import pika
import threading
import time
from medlabel.core.app import connect_redis
from config import RABBITMQ_HOST, RABBITMQ_PORT, RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD

class MessageHandler:
    def __init__(self):
        self.redis_client = connect_redis()
        self.connection = None
        self.channel = None
        
    def connect_rabbitmq(self):
        """连接RabbitMQ"""
        credentials = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
        self.connection = pika.BlockingConnection(pika.ConnectionParameters(
            host=RABBITMQ_HOST, 
            port=RABBITMQ_PORT, 
            credentials=credentials,
            heartbeat=300,
            blocked_connection_timeout=300
        ))
        self.channel = self.connection.channel()
        
    def process_image_convert_message(self, ch, method, properties, body):
        """处理图像转换消息"""
        try:
            # 解析JSON消息
            data = json.loads(body.decode('utf-8'))
            print(f"收到图像转换消息: {data}")
            
            task_id = data.get('taskId', 'unknown')
            
            # 设置初始进度
            self.redis_client.set(f'image_convert_task_progress:{task_id}', '0.1')
            
            # 模拟处理过程
            for i in range(1, 6):
                time.sleep(2)  # 模拟处理时间
                progress = i * 0.2
                self.redis_client.set(f'image_convert_task_progress:{task_id}', str(progress))
                print(f"任务 {task_id} 进度: {progress * 100}%")
            
            # 标记完成
            self.redis_client.set(f'image_convert_task_processed:{task_id}', '1')
            
            # 确认消息
            ch.basic_ack(delivery_tag=method.delivery_tag)
            print(f"任务 {task_id} 处理完成")
            
        except Exception as e:
            print(f"处理消息失败: {e}")
            # 拒绝消息
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
    
    def start_consuming(self):
        """开始消费消息"""
        self.connect_rabbitmq()
        
        # 设置队列消费
        self.channel.basic_consume(
            queue='medlabel_image_convert_queue',
            on_message_callback=self.process_image_convert_message
        )
        
        print('🚀 消息处理器启动，等待消息...')
        self.channel.start_consuming()

def run_message_handler():
    """运行消息处理器"""
    handler = MessageHandler()
    handler.start_consuming()

if __name__ == "__main__":
    run_message_handler()
