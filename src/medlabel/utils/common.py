"""
通用工具函数模块
包含各种通用的辅助函数
"""
import os
import time
import math
from functools import wraps


def set_permissions(path):
    """设置文件和目录权限"""
    for root, dirs, files in os.walk(path):
        if "deepzoom" not in root:
            continue
        os.chmod(root, 0o777)
        for d in dirs:
            os.chmod(os.path.join(root, d), 0o777)
        for f in files:
            os.chmod(os.path.join(root, f), 0o777)


def time_logger(func):
    """时间记录装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"函数 {func.__name__} 执行时间: {elapsed_time:.2f} 秒")
        return result
    return wrapper


def count_total_tile(max_level, width, height, tileSize):
    """计算总切片数量"""
    total_tiles = 0
    # 预先计算每个层级的切片数
    level_tile_counts = {}
    for level in range(max_level + 1):
        # 计算当前层的缩放比例
        scale_factor = 2 ** (max_level - level)
        scaled_height = math.ceil(height / scale_factor)
        scaled_width = math.ceil(width / scale_factor)

        # 计算该层的 Tile 数量
        num_tiles_x = math.ceil(scaled_width / tileSize)
        num_tiles_y = math.ceil(scaled_height / tileSize)

        # 存储每个层级的 Tile 数量
        level_tile_counts[level] = (num_tiles_x, num_tiles_y)

        # 累加该层的总切片数
        total_tiles += num_tiles_x * num_tiles_y
    
    # 打印总层数和总切片数
    print(f"Total levels: {max_level + 1}")
    print(f"Total tiles: {total_tiles}")
    return level_tile_counts


def generate_metadata(width, height, outputDir, tileSize):
    """生成深度缩放元数据文件"""
    metadata = f"""<Image TileSize="{tileSize}" Overlap="0" Format="JPEG" xmlns="http://schemas.microsoft.com/deepzoom/2008">
  <Size Width="{str(width)}" Height="{str(height)}" /></Image>"""
    with open(os.path.join(outputDir, "metadata.xml"), "w") as f:
        f.write(metadata)


def ensure_directory_exists(directory_path):
    """确保目录存在，如果不存在则创建"""
    os.makedirs(directory_path, exist_ok=True)


def validate_required_params(*params):
    """验证必需参数是否都存在"""
    return all(param is not None and param != '' for param in params)


def safe_int_conversion(value, default=0):
    """安全的整数转换"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def safe_float_conversion(value, default=0.0):
    """安全的浮点数转换"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default
