
import logging
import openslide
import numpy as np
import cv2, requests
from colorama import Fore
from datetime import datetime
from config import HOST_UPLOADS

logger = logging.getLogger('app.utils.util')

def map_host(img_path):
    img_path = img_path.replace('/uploads', HOST_UPLOADS)

    return img_path
    return '.'.join(img_path.split('.')[:-2])

class Timer:
    def __init__(self):
        self.st = datetime.now()

    @classmethod
    def now(cls):
        print(Fore.GREEN + f"===> Time Now: {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}")

    def start(self):
        self.st = datetime.now()

    def strip_time(self, t, cost=False):
        if not cost:
            strs = "{:02d}h:{:02d}m:{:02d}s".format(t.hour, t.minute, t.second % 60)
        else:
            strs = "{:02d}h:{:02d}m:{:02d}s".format(t.seconds // 3600, (t.seconds // 60) % 60, t.seconds % 60)
            
        return strs

    def end(self):
        st_str = self.strip_time(self.st)
        
        cur = datetime.now()
        end_str = self.strip_time(cur)
        
        cost = (cur - self.st)
        cost_str = self.strip_time(cost, cost=True)
        
        print(Fore.GREEN + f"===> Begin: {st_str}, End: {end_str}, Time Cost: {cost_str}")


class ImageFetcher:
    # 图像数据获取助手类
    def __init__(self):
        # 使用requests.Session 来优化多次网络请求时的性能
        self.session = requests.Session()
        
    def fetch_image(self, img_url):
        """从给定的URL获取图像, 并返回OpenCV格式的图像"""
        try:
            response = self.session.get(img_url, proxies={}, verify=False)
            img_bytes = response.content
            # 将图像数据转换为 NumPy 数组
            nparr = np.frombuffer(img_bytes, np.uint8)
            # 解码图像数据为 OpenCV 格式
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            return image
        
        except requests.exceptions.RequestException as e:
            print(f"【错误】app.utils.util 请求图像时发生错误: {e}")
            logger.error(f"请求图像时发生错误: {e}")
            return None
        
    def close_session(self):
        self.session.close()
