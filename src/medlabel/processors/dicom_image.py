"""
DICOM图像处理器
处理DICOM医学图像文件的转换和标准化
"""
import os
import numpy as np
import SimpleIT<PERSON> as sitk
from typing import Dict, Any, Optional
from .base import BaseImageProcessor, ProcessingResult
from .normal_image import NormalImageProcessor
from config import PROJECT_SAVE_DIR


class DicomImageProcessor(BaseImageProcessor):
    """DICOM图像处理器"""
    
    def __init__(self):
        super().__init__()
        self.normal_processor = NormalImageProcessor()
    
    def process(self, data: Dict[str, Any], reply_channel: Optional[Any] = None) -> bool:
        """
        处理DICOM图像
        
        Args:
            data: 包含处理参数的字典
            reply_channel: 可选的回复通道
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 提取参数
            taskId = data.get('taskId')
            projectId = data.get('projectId')
            imageName = data.get('imageName')
            imageId = data.get('imageId')
            imageUrl = data.get('imageUrl')
            outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId))

            # 验证参数
            if not self.validate_common_params(data):
                self.send_error_message(taskId, imageId, "缺少必需参数", reply_channel)
                return False

            # 创建输出目录
            if not self.create_output_directory(outputDir):
                self.send_error_message(taskId, imageId, "创建输出目录失败", reply_channel)
                return False

            # 转换DICOM图像
            result = self._convert_dicom_image(imageUrl, outputDir, imageName)
            if not result:
                self.send_error_message(taskId, imageId, result.message, reply_channel)
                return False

            # 生成缩略图
            result = self.normal_processor._generate_thumbnail(outputDir, imageName)
            if not result:
                self.send_error_message(taskId, imageId, result.message, reply_channel)
                return False

            # 发送成功消息
            self.send_success_message(taskId, imageId, f"{taskId}: DICOM图像转化任务完成", reply_channel)
            return True

        except Exception as e:
            print(f"DICOM图像处理异常: {e}")
            if 'taskId' in locals() and 'imageId' in locals():
                self.send_error_message(taskId, imageId, f"处理异常: {str(e)}", reply_channel)
            return False

    def _convert_dicom_image(self, imageUrl: str, outputDir: str, imageName: str) -> ProcessingResult:
        """转换DICOM图像"""
        try:
            # 读取DICOM图像
            ds_array = sitk.ReadImage(imageUrl)
            pixel_array = sitk.GetArrayFromImage(ds_array)
            
            # 标准化像素值
            normalized_array = self._normalize_pixel_array(pixel_array)
            
            # 转换为SimpleITK图像并保存
            img = sitk.GetImageFromArray(np.array(normalized_array).astype('uint8'))
            output_path = os.path.join(outputDir, f"{imageName}.png")
            sitk.WriteImage(img, output_path)
            
            # 设置文件权限
            self.set_file_permissions(output_path)
            
            return ProcessingResult(True, "DICOM图像转换成功")
            
        except Exception as e:
            return ProcessingResult(False, f"DICOM图像转换失败: {str(e)}")

    def _normalize_pixel_array(self, pixel_array: np.ndarray) -> list:
        """标准化像素数组"""
        normalized_array = []
        for z in pixel_array:
            y_array = []
            for y in z:
                x_array = []
                for x in y:
                    normalized_x = self._normalize_pixel_value(x)
                    x_array.append(normalized_x)
                y_array.append(x_array)
            normalized_array.append(y_array)
        return normalized_array

    def _normalize_pixel_value(self, x: float, window_width: int = 1800, window_level: int = 1000) -> int:
        """
        标准化单个像素值
        
        Args:
            x: 原始像素值
            window_width: 窗宽
            window_level: 窗位
            
        Returns:
            int: 标准化后的像素值 (0-255)
        """
        low = window_level - window_width / 2
        high = window_level + window_width / 2

        if x < low:
            return 0
        elif x > high:
            return 255
        else:
            return int((x - low) * (255 / window_width))

    def set_window_parameters(self, window_width: int, window_level: int):
        """设置窗宽窗位参数"""
        self.window_width = window_width
        self.window_level = window_level

    def get_dicom_metadata(self, imageUrl: str) -> Dict[str, Any]:
        """获取DICOM元数据"""
        try:
            ds_array = sitk.ReadImage(imageUrl)
            metadata = {}
            
            # 获取基本信息
            metadata['size'] = ds_array.GetSize()
            metadata['spacing'] = ds_array.GetSpacing()
            metadata['origin'] = ds_array.GetOrigin()
            metadata['direction'] = ds_array.GetDirection()
            
            # 获取像素类型信息
            metadata['pixel_type'] = sitk.GetPixelIDValueAsString(ds_array.GetPixelID())
            metadata['number_of_components'] = ds_array.GetNumberOfComponentsPerPixel()
            
            return metadata
            
        except Exception as e:
            print(f"获取DICOM元数据失败: {e}")
            return {}
