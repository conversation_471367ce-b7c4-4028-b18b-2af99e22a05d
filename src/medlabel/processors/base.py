"""
图像处理器基础类
定义所有图像处理器的通用接口和行为
"""
import os
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from ..utils.common import ensure_directory_exists, validate_required_params
from ..core.connections import connection_manager


class BaseImageProcessor(ABC):
    """图像处理器基础类"""
    
    def __init__(self):
        self.connection_manager = connection_manager
    
    @abstractmethod
    def process(self, data: Dict[str, Any], reply_channel: Optional[Any] = None) -> bool:
        """
        处理图像的抽象方法
        
        Args:
            data: 包含处理参数的字典
            reply_channel: 可选的回复通道
            
        Returns:
            bool: 处理是否成功
        """
        pass
    
    def validate_common_params(self, data: Dict[str, Any]) -> bool:
        """验证通用参数"""
        required_params = ['taskId', 'projectId', 'imageUrl', 'imageName']
        return validate_required_params(*[data.get(param) for param in required_params])
    
    def send_error_message(self, taskId: str, imageId: str, error_msg: str, reply_channel: Optional[Any] = None):
        """发送错误消息"""
        print(f"处理错误: taskId={taskId}, imageId={imageId}, error={error_msg} (队列消息发送已禁用)")
        # 注释掉队列消息发送功能
        # if reply_channel:
        #     reply_channel.basic_publish(
        #         exchange='',
        #         routing_key='medlabel_image_convert_task_finish_callback_queue',
        #         body=json.dumps({
        #             "taskId": taskId,
        #             "imageId": imageId,
        #             "status": 3,
        #             "progress": 0.0,
        #             "result": error_msg
        #         })
        #     )
    
    def send_success_message(self, taskId: str, imageId: str, message: str, reply_channel: Optional[Any] = None):
        """发送成功消息"""
        print(f"处理成功: taskId={taskId}, imageId={imageId}, message={message} (队列消息发送已禁用)")
        # 注释掉队列消息发送功能
        # if reply_channel:
        #     reply_channel.basic_publish(
        #         exchange='',
        #         routing_key='medlabel_image_convert_task_finish_callback_queue',
        #         body=json.dumps({
        #             "taskId": taskId,
        #             "imageId": imageId,
        #             "status": 2,
        #             "progress": 1.0,
        #             "result": message
        #         })
        #     )
    
    def create_output_directory(self, output_dir: str) -> bool:
        """创建输出目录"""
        try:
            ensure_directory_exists(output_dir)
            return True
        except Exception as e:
            print(f"创建输出目录失败: {e}")
            return False
    
    def set_file_permissions(self, file_path: str):
        """设置文件权限"""
        try:
            os.chmod(file_path, 0o777)
        except Exception as e:
            print(f"设置文件权限失败: {e}")


class ProcessingResult:
    """处理结果类"""
    
    def __init__(self, success: bool, message: str = "", data: Any = None):
        self.success = success
        self.message = message
        self.data = data
    
    def __bool__(self):
        return self.success
