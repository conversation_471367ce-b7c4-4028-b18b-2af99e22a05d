"""
普通图像处理器
处理常规图像文件的复制和缩略图生成
"""
import os
import shutil
from typing import Dict, Any, Optional
from PIL import Image
from .base import BaseImageProcessor, ProcessingResult
from config import PROJECT_SAVE_DIR

# 设置PIL最大图像像素限制
Image.MAX_IMAGE_PIXELS = None


class NormalImageProcessor(BaseImageProcessor):
    """普通图像处理器"""
    
    def process(self, data: Dict[str, Any], reply_channel: Optional[Any] = None) -> bool:
        """
        处理普通图像
        
        Args:
            data: 包含处理参数的字典
            reply_channel: 可选的回复通道
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 提取参数
            taskId = data.get('taskId')
            projectId = data.get('projectId')
            imageName = data.get('imageName')
            imageId = data.get('imageId')
            imageUrl = data.get('imageUrl')
            outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId))

            # 验证参数
            if not self.validate_common_params(data):
                self.send_error_message(taskId, imageId, "缺少必需参数", reply_channel)
                return False

            # 创建输出目录
            if not self.create_output_directory(outputDir):
                self.send_error_message(taskId, imageId, "创建输出目录失败", reply_channel)
                return False

            # 复制原始图像
            result = self._copy_original_image(imageUrl, outputDir, imageName)
            if not result:
                self.send_error_message(taskId, imageId, result.message, reply_channel)
                return False

            # 生成缩略图
            result = self._generate_thumbnail(outputDir, imageName)
            if not result:
                self.send_error_message(taskId, imageId, result.message, reply_channel)
                return False

            # 发送成功消息
            self.send_success_message(taskId, imageId, f"{taskId}: 图像转化任务完成", reply_channel)
            return True

        except Exception as e:
            print(f"普通图像处理异常: {e}")
            if 'taskId' in locals() and 'imageId' in locals():
                self.send_error_message(taskId, imageId, f"处理异常: {str(e)}", reply_channel)
            return False

    def _copy_original_image(self, imageUrl: str, outputDir: str, imageName: str) -> ProcessingResult:
        """复制原始图像"""
        try:
            output_path = os.path.join(outputDir, f"{imageName}.png")
            shutil.copy(imageUrl, output_path)
            self.set_file_permissions(output_path)
            return ProcessingResult(True, "图像复制成功")
        except Exception as e:
            return ProcessingResult(False, f"图像复制失败: {str(e)}")

    def _generate_thumbnail(self, outputDir: str, imageName: str) -> ProcessingResult:
        """生成缩略图"""
        try:
            input_path = os.path.join(outputDir, f"{imageName}.png")
            thumbnail_dir = os.path.join(outputDir, "thumbnail")
            
            # 创建缩略图目录
            if not self.create_output_directory(thumbnail_dir):
                return ProcessingResult(False, "创建缩略图目录失败")
            
            # 生成缩略图
            result = self._create_thumbnail_for_single_image(input_path, thumbnail_dir, imageName)
            return result
            
        except Exception as e:
            return ProcessingResult(False, f"生成缩略图失败: {str(e)}")

    def _create_thumbnail_for_single_image(self, input_path: str, output_path: str, imageName: str) -> ProcessingResult:
        """为单个图像创建缩略图"""
        try:
            with Image.open(input_path) as img:
                img = img.resize((224, 224), Image.Resampling.LANCZOS)
                thumbnail_path = os.path.join(output_path, f"{imageName}.png")
                img.save(thumbnail_path)
                self.set_file_permissions(thumbnail_path)
            
            print(f"已处理缩略图：{input_path}")
            return ProcessingResult(True, "缩略图生成成功")
            
        except Exception as e:
            print(f"处理缩略图 {input_path} 时出错：{e}")
            return ProcessingResult(False, f"缩略图处理失败: {str(e)}")

    def batch_thumbnail_convert(self, input_dir: str, output_dir: str) -> ProcessingResult:
        """批量转换缩略图"""
        try:
            # 确保输出目录存在
            if not self.create_output_directory(output_dir):
                return ProcessingResult(False, "创建输出目录失败")

            # 遍历输入文件夹中的所有文件
            processed_count = 0
            for filename in os.listdir(input_dir):
                if filename.lower().endswith('.png'):
                    input_path = os.path.join(input_dir, filename)
                    output_path = os.path.join(output_dir, filename)

                    try:
                        # 打开图像并转换尺寸
                        with Image.open(input_path) as img:
                            img = img.resize((224, 224), Image.Resampling.LANCZOS)
                            img.save(output_path)
                        print(f"已处理：{filename}")
                        processed_count += 1
                    except Exception as e:
                        print(f"处理 {filename} 时出错：{e}")

            return ProcessingResult(True, f"批量处理完成，共处理 {processed_count} 个文件")
            
        except Exception as e:
            return ProcessingResult(False, f"批量处理失败: {str(e)}")
