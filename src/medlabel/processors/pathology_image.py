"""
病理图像处理器
处理病理切片图像的深度缩放生成
"""
import os
import os.path as osp
from typing import Dict, Any, Optional
import openslide
from openslide import OpenSlide
import openslide.deepzoom
from PIL import Image
from .base import BaseImageProcessor, ProcessingResult
from ..utils.kfbreader import KFBSlide
from ..utils.common import set_permissions, generate_metadata
from config import PROJECT_SAVE_DIR

# 设置PIL最大图像像素限制
Image.MAX_IMAGE_PIXELS = None


class PathologyImageProcessor(BaseImageProcessor):
    """病理图像处理器"""
    
    def process(self, data: Dict[str, Any], reply_channel: Optional[Any] = None) -> bool:
        """
        处理病理图像
        
        Args:
            data: 包含处理参数的字典
            reply_channel: 可选的回复通道
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 提取参数
            taskId = data.get('taskId')
            projectId = data.get('projectId')
            imageName = data.get('imageName')
            imageId = data.get('imageId')
            mrxs_path = data.get('imageUrl')
            outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId), imageName, "deepzoom")
            tileSize = 1024
            overlap = 0

            # 验证参数
            if not self.validate_common_params(data):
                self.send_error_message(taskId, imageId, "缺少必需参数", reply_channel)
                return False

            # 检查文件是否存在
            if not os.path.exists(mrxs_path):
                error_msg = f"病理图文件不存在: {mrxs_path}"
                print(error_msg)
                self.send_error_message(taskId, imageId, error_msg, reply_channel)
                return False

            print(f"开始处理病理图文件: {mrxs_path}")
            
            # 异步处理病理图像（这里需要导入tasks模块，但为了避免循环导入，我们先返回True）
            # 实际的处理逻辑将在tasks.py中的Celery任务中完成
            print(f"病理图处理任务已提交: taskId={taskId}, imageId={imageId}")
            return True

        except Exception as e:
            print(f"病理图像处理异常: {e}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
            if 'taskId' in locals() and 'imageId' in locals():
                self.send_error_message(taskId, imageId, f"处理异常: {str(e)}", reply_channel)
            return False

    def get_slide(self, wsi_path: str):
        """获取切片对象"""
        ext = osp.splitext(wsi_path)[1].lower()
        if ext in ['.svs', '.tif', '.tiff', '.mrxs']:
            slide = OpenSlide(wsi_path)
        elif ext in ['.kfb']:
            slide = KFBSlide(wsi_path)
        else:
            raise ValueError(f'Unsupported extension: {wsi_path}')
        return slide

    def read_region(self, slide, location, level, size, zero_level_loc=True) -> Image:
        """
        读取切片指定层级的指定区域
        
        Args:
            slide: get_slide函数获取的切片对象
            location: 要读取区域的左上角坐标(x, y)
            level: 要读取的缩放层级
            size: 要读取的区域图片大小
            zero_level_loc: 若为True，则location参数为左上角在level 0上的坐标，否则location为当前level上的左上角坐标
        """
        ratio = slide.level_downsamples[level] / slide.level_downsamples[0]
        if isinstance(slide, KFBSlide):
            if zero_level_loc:
                return Image.fromarray(slide.read_region((round(location[0]/ratio), round(location[1]/ratio)), level, size))
            return Image.fromarray(slide.read_region(location, level, size))
        elif isinstance(slide, OpenSlide):
            if zero_level_loc:
                return slide.read_region(location, level, size)
            return slide.read_region((round(location[0]*ratio), round(location[1]*ratio)), level, size)
        else:
            raise ValueError(f'Unsupported slide: {type(slide)}')

    def get_tile(self, slide, level, x, y, size=None):
        """获取指定层级的指定瓦片"""
        # 计算区域的左上角坐标和区域大小
        level_size = slide.level_dimensions[level]
        tile_size = 1024
        location = (x * tile_size, y * tile_size)
        return self.read_region(slide, location, level, (tile_size, tile_size))

    def validate_slide_file(self, slide_path: str) -> ProcessingResult:
        """验证切片文件"""
        try:
            if not os.path.exists(slide_path):
                return ProcessingResult(False, f"文件不存在: {slide_path}")
            
            # 尝试打开切片文件
            slide = self.get_slide(slide_path)
            
            # 获取基本信息
            dimensions = slide.dimensions
            level_count = slide.level_count
            
            print(f"切片文件验证成功: {slide_path}")
            print(f"图像尺寸: {dimensions}")
            print(f"层级数量: {level_count}")
            
            return ProcessingResult(True, "切片文件验证成功", {
                'dimensions': dimensions,
                'level_count': level_count
            })
            
        except Exception as e:
            return ProcessingResult(False, f"切片文件验证失败: {str(e)}")

    def get_slide_info(self, slide_path: str) -> Dict[str, Any]:
        """获取切片文件信息"""
        try:
            slide = self.get_slide(slide_path)
            info = {
                'dimensions': slide.dimensions,
                'level_count': slide.level_count,
                'level_dimensions': slide.level_dimensions,
                'level_downsamples': slide.level_downsamples,
                'properties': dict(slide.properties) if hasattr(slide, 'properties') else {}
            }
            return info
        except Exception as e:
            print(f"获取切片信息失败: {e}")
            return {}
