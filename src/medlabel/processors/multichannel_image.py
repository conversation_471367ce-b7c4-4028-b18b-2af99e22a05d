"""
多通道图像处理器
处理多通道TIFF图像的通道分离和深度缩放生成
"""
import os
import math
import numpy as np
import tifffile as tiff
from typing import Dict, Any, Optional, Tuple, List
from PIL import Image
from billiard import Pool
from .base import BaseImageProcessor, ProcessingResult
from ..utils.common import count_total_tile, generate_metadata, set_permissions
from config import PROJECT_SAVE_DIR

# 设置PIL最大图像像素限制
Image.MAX_IMAGE_PIXELS = None

# 全局变量用于多进程处理
global_img = None


def init_worker(shared_img):
    """初始化工作进程"""
    global global_img
    global_img = shared_img


def process_channel(i, outputDir):
    """处理单个通道"""
    # 从全局变量中取出通道
    channel = global_img[i]
    output_path = os.path.join(outputDir, f'channel_{i+1}.tiff')
    tiff.imwrite(output_path, channel)
    return f"[PID {os.getpid()}] 写入通道 {i+1}"


class MultichannelImageProcessor(BaseImageProcessor):
    """多通道图像处理器"""
    
    def process(self, data: Dict[str, Any], reply_channel: Optional[Any] = None) -> bool:
        """
        处理多通道图像
        
        Args:
            data: 包含处理参数的字典
            reply_channel: 可选的回复通道
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 提取参数
            taskId = data.get('taskId')
            projectId = data.get('projectId')
            imageName = data.get('imageName')
            imageId = data.get('imageId')
            mrxs_path = data.get('imageUrl')
            outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId), imageName, "split")
            tileSize = 1024
            overlap = 0

            # 验证参数
            if not self.validate_common_params(data):
                self.send_error_message(taskId, imageId, "缺少必需参数", reply_channel)
                return False

            # 检查文件是否存在
            if not os.path.exists(mrxs_path):
                error_msg = f"多通道图文件不存在: {mrxs_path}"
                print(error_msg)
                self.send_error_message(taskId, imageId, error_msg, reply_channel)
                return False

            # 创建输出目录
            if not self.create_output_directory(outputDir):
                self.send_error_message(taskId, imageId, "创建输出目录失败", reply_channel)
                return False

            print(f"开始处理多通道图文件: {mrxs_path}")
            
            # 异步处理多通道图像（实际处理逻辑将在tasks.py中完成）
            print(f"多通道图处理任务已提交: taskId={taskId}, imageId={imageId}")
            return True

        except Exception as e:
            print(f"多通道图像处理异常: {e}")
            if 'taskId' in locals() and 'imageId' in locals():
                self.send_error_message(taskId, imageId, f"处理异常: {str(e)}", reply_channel)
            return False

    def split_channels(self, imageUrl: str, outputDir: str, projectId: str, imageName: str) -> Tuple[List[str], List[str]]:
        """
        分离多通道图像
        
        Args:
            imageUrl: 输入图像路径
            outputDir: 输出目录
            projectId: 项目ID
            imageName: 图像名称
            
        Returns:
            Tuple[List[str], List[str]]: (通道文件路径列表, 输出目录列表)
        """
        try:
            # 读取多通道图像
            img = tiff.imread(imageUrl)
            print("读取多通道TIFF图像完成")
            
            channels = img.shape[0]
            print(f"检测到 {channels} 个通道")
            
            # 准备多进程任务
            tasks = [(i, outputDir) for i in range(channels)]

            # 使用多进程处理通道分离
            with Pool(processes=channels, initializer=init_worker, initargs=(img,)) as pool:
                results = []
                for args in tasks:
                    r = pool.apply_async(process_channel, args=args)
                    results.append(r)
                pool.close()
                pool.join()
                
                # 打印处理结果
                for r in results:
                    print(r.get())

            # 生成文件路径列表
            tiffPaths, outputDirs = [], []
            for i in range(channels):
                tiffPaths.append(os.path.join(outputDir, f'channel_{i+1}.tiff'))
                outputDirs.append(os.path.join(PROJECT_SAVE_DIR, str(projectId), imageName, str(i+1)))
            
            return tiffPaths, outputDirs
            
        except Exception as e:
            print(f"通道分离失败: {e}")
            return [], []

    def generate_deep_zoom_for_tiff(self, mrxs_path: str, outputDir: str, tileSize: int, 
                                   overlap: int, taskId: str, imageId: str) -> ProcessingResult:
        """
        为TIFF图像生成深度缩放
        
        Args:
            mrxs_path: 输入TIFF文件路径
            outputDir: 输出目录
            tileSize: 瓦片大小
            overlap: 重叠像素
            taskId: 任务ID
            imageId: 图像ID
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            # 读取图像
            image = tiff.imread(mrxs_path)

            if image.ndim != 2:
                return ProcessingResult(False, f"图像不是灰度图（2D），而是 {image.shape}")

            # 若不是 uint8，做简单归一化
            if image.dtype != np.uint8:
                image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)

            height, width = image.shape[:2]
            max_level = int(math.ceil(math.log(max(height, width), 2)))

            level_tile_counts = count_total_tile(max_level, width, height, tileSize)

            # 处理每个层级的切片
            for level in range(max_level + 1):
                scale_factor = 2 ** (max_level - level)
                scaled_width = math.ceil(width / scale_factor)
                scaled_height = math.ceil(height / scale_factor)

                # 使用 PIL 对整个图像进行缩放
                pil_image = Image.fromarray(image)
                level_image = pil_image.resize((scaled_width, scaled_height), Image.Resampling.LANCZOS)

                level_dir = os.path.join(outputDir, "imgs", str(level))
                os.makedirs(level_dir, exist_ok=True)

                num_tiles_x, num_tiles_y = level_tile_counts[level]

                for x in range(num_tiles_x):
                    for y in range(num_tiles_y):
                        left = x * tileSize
                        top = y * tileSize
                        right = min(left + tileSize, scaled_width)
                        bottom = min(top + tileSize, scaled_height)

                        if right <= left or bottom <= top:
                            continue

                        # 从缩放后的图像裁剪出 tile
                        tile = level_image.crop((left, top, right, bottom))

                        tile_filename = os.path.join(level_dir, f"{x}_{y}.jpeg")
                        tile.save(tile_filename, "JPEG")

                set_permissions(level_dir)

            # 生成元数据
            generate_metadata(width, height, outputDir, tileSize)
            os.chmod(os.path.join(outputDir, 'metadata.xml'), 0o777)

            return ProcessingResult(True, "TIFF深度缩放生成成功")

        except Exception as e:
            error_msg = f"生成TIFF深度图时发生异常: {e}"
            print(error_msg)
            return ProcessingResult(False, error_msg)

    def validate_multichannel_image(self, image_path: str) -> ProcessingResult:
        """验证多通道图像"""
        try:
            img = tiff.imread(image_path)
            
            if img.ndim < 3:
                return ProcessingResult(False, "不是多通道图像")
            
            channels = img.shape[0]
            height, width = img.shape[1], img.shape[2]
            
            print(f"多通道图像验证成功: {image_path}")
            print(f"通道数: {channels}, 尺寸: {width}x{height}")
            
            return ProcessingResult(True, "多通道图像验证成功", {
                'channels': channels,
                'width': width,
                'height': height,
                'dtype': str(img.dtype)
            })
            
        except Exception as e:
            return ProcessingResult(False, f"多通道图像验证失败: {str(e)}")

    def get_channel_statistics(self, image_path: str) -> Dict[str, Any]:
        """获取各通道统计信息"""
        try:
            img = tiff.imread(image_path)
            stats = {}
            
            for i in range(img.shape[0]):
                channel = img[i]
                stats[f'channel_{i+1}'] = {
                    'min': float(np.min(channel)),
                    'max': float(np.max(channel)),
                    'mean': float(np.mean(channel)),
                    'std': float(np.std(channel))
                }
            
            return stats
            
        except Exception as e:
            print(f"获取通道统计信息失败: {e}")
            return {}
