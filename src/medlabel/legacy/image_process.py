"""
图像处理主模块 - 重构后的协调器
负责协调各个图像处理器和任务的执行

重构说明:
- 原始的大型单体文件已被拆分为多个专门的模块
- 连接管理: app.connections
- 图像处理器: app.image_processors.*
- Celery任务: app.tasks
- 消息处理: app.message_handler
- 工具函数: app.utils
"""

# 导入重构后的模块
from .message_handler import message_handler
from .connections import connection_manager
from .image_processors import (
    NormalImageProcessor,
    DicomImageProcessor,
    PathologyImageProcessor,
    MultichannelImageProcessor
)

# 为了保持向后兼容性，保留原始函数名作为别名

# ================================= 向后兼容性函数 ==================================

# 为了保持向后兼容性，提供原始函数的别名
def get_rabbitmq_connection():
    """向后兼容：RabbitMQ连接上下文管理器"""
    return connection_manager.get_rabbitmq_connection()

def send_progress_update(taskId, imageId, status, progress, result):
    """向后兼容：发送进度更新消息"""
    return connection_manager.send_progress_update(taskId, imageId, status, progress, result)

# ================================= 向后兼容性函数 (续) ==================================

# 普通图像处理函数别名
def thumbnail_convert(input_dir, output_dir):
    """向后兼容：批量缩略图转换"""
    processor = NormalImageProcessor()
    return processor.batch_thumbnail_convert(input_dir, output_dir)

def thumbnail_convert_for_single_image(input_path, output_path, imageName):
    """向后兼容：单个图像缩略图转换"""
    processor = NormalImageProcessor()
    return processor._create_thumbnail_for_single_image(input_path, output_path, imageName)

# 病理图像处理函数别名
def get_slide(wsi_path):
    """向后兼容：获取切片对象"""
    processor = PathologyImageProcessor()
    return processor.get_slide(wsi_path)

def read_region(slide, location, level, size, zero_level_loc=True):
    """向后兼容：读取切片指定层级的指定区域"""
    processor = PathologyImageProcessor()
    return processor.read_region(slide, location, level, size, zero_level_loc)

def get_tile(slide, level, x, y, size=None):
    """向后兼容：获取指定层级的指定瓦片"""
    processor = PathologyImageProcessor()
    return processor.get_tile(slide, level, x, y, size)

# ================================= 主要入口函数 ==================================

def image_process(channel, method, properties, body):
    """
    图像处理主入口函数 - 重构后的版本

    Args:
        channel: RabbitMQ通道
        method: 消息方法
        properties: 消息属性
        body: 消息体

    Returns:
        处理结果
    """
    return message_handler.handle_image_process_message(channel, method, properties, body)

# ================================= 向后兼容性函数 (续2) ==================================

# DICOM处理函数别名
def normalization(x):
    """向后兼容：像素值标准化"""
    processor = DicomImageProcessor()
    return processor._normalize_pixel_value(x)

def dicom_convert(imageUrl, outputDir, imageName):
    """向后兼容：DICOM图像转换"""
    processor = DicomImageProcessor()
    return processor._convert_dicom_image(imageUrl, outputDir, imageName)

# 多通道图像处理函数别名
def init_worker(shared_img):
    """向后兼容：初始化工作进程"""
    from .image_processors.multichannel_image import init_worker as _init_worker
    return _init_worker(shared_img)

def process_channel(i, outputDir):
    """向后兼容：处理单个通道"""
    from .image_processors.multichannel_image import process_channel as _process_channel
    return _process_channel(i, outputDir)

# 工具函数别名
def count_total_tile(max_level, width, height, tileSize):
    """向后兼容：计算总切片数量"""
    from .utils import count_total_tile as _count_total_tile
    return _count_total_tile(max_level, width, height, tileSize)

# 图像类型处理函数别名（保持向后兼容）
def normal_image_process(data, reply_channel):
    """向后兼容：普通图像处理"""
    processor = NormalImageProcessor()
    return processor.process(data, reply_channel)

def dicom_image_process(data, reply_channel):
    """向后兼容：DICOM图像处理"""
    processor = DicomImageProcessor()
    return processor.process(data, reply_channel)

def patho_image_process(data, reply_channel):
    """向后兼容：病理图像处理"""
    processor = PathologyImageProcessor()
    return processor.process(data, reply_channel)

def channel_image_process(data, reply_channel):
    """向后兼容：多通道图像处理"""
    processor = MultichannelImageProcessor()
    return processor.process(data, reply_channel)