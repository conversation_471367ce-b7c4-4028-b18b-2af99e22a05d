"""
消息处理模块
负责处理来自消息队列的图像处理请求
"""
import json
import traceback
from typing import Dict, Any, Optional
from ..processors import (
    NormalImageProcessor,
    DicomImageProcessor,
    PathologyImageProcessor,
    MultichannelImageProcessor
)
from .tasks import generate_deep_zoom, channel_convert, generate_deep_zoom_for_TIFF


class MessageHandler:
    """消息处理器，负责分发不同类型的图像处理任务"""
    
    def __init__(self):
        self.processors = {
            1: NormalImageProcessor(),      # 普通图像
            2: DicomImageProcessor(),       # DICOM图像
            3: PathologyImageProcessor(),   # 病理图像
            4: MultichannelImageProcessor() # 多通道图像
        }
    
    def handle_image_process_message(self, channel, method, properties, body):
        """
        处理图像处理消息
        
        Args:
            channel: RabbitMQ通道
            method: 消息方法
            properties: 消息属性
            body: 消息体
        """
        try:
            print("接受到消息，开始执行图像转化任务")
            
            # 解析消息
            data = json.loads(body.decode('utf-8'))
            print(f"消息内容: {data}")
            
            taskId = data.get('taskId')
            imageTypeId = data.get('imageTypeId')
            imageId = data.get('imageId')
            
            # 设置回复通道为None（已禁用队列消息发送）
            reply_channel = None
            
            # 根据图像类型分发处理任务
            success = self._dispatch_processing_task(imageTypeId, data, reply_channel)
            
            if not success:
                print(f"图像处理失败: taskId={taskId}, imageId={imageId}, imageTypeId={imageTypeId}")
            
        except Exception as e:
            print(f"消息处理主函数异常: {e}")
            print(f"详细错误信息: {traceback.format_exc()}")
            
            # 尝试发送错误消息（如果可能的话）
            try:
                if 'data' in locals():
                    taskId = data.get('taskId', 'unknown')
                    imageId = data.get('imageId', 'unknown')
                    print(f"主函数异常: taskId={taskId}, imageId={imageId}, error={str(e)} (队列消息发送已禁用)")
            except Exception as publish_error:
                print(f"发送错误消息失败: {publish_error}")
    
    def _dispatch_processing_task(self, imageTypeId: int, data: Dict[str, Any], reply_channel: Optional[Any]) -> bool:
        """
        分发处理任务
        
        Args:
            imageTypeId: 图像类型ID
            data: 处理数据
            reply_channel: 回复通道
            
        Returns:
            bool: 处理是否成功
        """
        try:
            if imageTypeId == 1:
                # 普通图像处理
                return self._handle_normal_image(data, reply_channel)
            elif imageTypeId == 2:
                # DICOM图像处理
                return self._handle_dicom_image(data, reply_channel)
            elif imageTypeId == 3:
                # 病理图像处理
                return self._handle_pathology_image(data, reply_channel)
            elif imageTypeId == 4:
                # 多通道图像处理
                return self._handle_multichannel_image(data, reply_channel)
            else:
                # 未知图像类型
                taskId = data.get('taskId', 'unknown')
                imageId = data.get('imageId', 'unknown')
                print(f"imageTypeId无法识别: taskId={taskId}, imageId={imageId}, imageTypeId={imageTypeId} (队列消息发送已禁用)")
                return False
                
        except Exception as e:
            print(f"任务分发异常: {e}")
            return False
    
    def _handle_normal_image(self, data: Dict[str, Any], reply_channel: Optional[Any]) -> bool:
        """处理普通图像"""
        processor = self.processors[1]
        return processor.process(data, reply_channel)
    
    def _handle_dicom_image(self, data: Dict[str, Any], reply_channel: Optional[Any]) -> bool:
        """处理DICOM图像"""
        processor = self.processors[2]
        return processor.process(data, reply_channel)
    
    def _handle_pathology_image(self, data: Dict[str, Any], reply_channel: Optional[Any]) -> bool:
        """处理病理图像"""
        try:
            taskId = data.get('taskId')
            projectId = data.get('projectId')
            imageName = data.get('imageName')
            imageId = data.get('imageId')
            mrxs_path = data.get('imageUrl')
            
            # 基本验证
            processor = self.processors[3]
            if not processor.validate_common_params(data):
                processor.send_error_message(taskId, imageId, "缺少必需参数", reply_channel)
                return False
            
            # 检查文件是否存在
            import os
            if not os.path.exists(mrxs_path):
                error_msg = f"病理图文件不存在: {mrxs_path}"
                print(error_msg)
                processor.send_error_message(taskId, imageId, error_msg, reply_channel)
                return False
            
            # 提交Celery异步任务
            from config import PROJECT_SAVE_DIR
            outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId), imageName, "deepzoom")
            tileSize = 1024
            overlap = 0
            
            print(f"开始处理病理图文件: {mrxs_path}")
            task = generate_deep_zoom.apply_async(args=[mrxs_path, outputDir, tileSize, overlap, taskId, imageId])
            print(f"Celery任务已提交: {task.id}")
            
            return True
            
        except Exception as e:
            print(f"病理图像处理异常: {e}")
            print(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def _handle_multichannel_image(self, data: Dict[str, Any], reply_channel: Optional[Any]) -> bool:
        """处理多通道图像"""
        try:
            taskId = data.get('taskId')
            projectId = data.get('projectId')
            imageName = data.get('imageName')
            imageId = data.get('imageId')
            mrxs_path = data.get('imageUrl')
            
            # 基本验证
            processor = self.processors[4]
            if not processor.validate_common_params(data):
                processor.send_error_message(taskId, imageId, "缺少必需参数", reply_channel)
                return False
            
            # 检查文件是否存在
            import os
            if not os.path.exists(mrxs_path):
                error_msg = f"多通道图文件不存在: {mrxs_path}"
                print(error_msg)
                processor.send_error_message(taskId, imageId, error_msg, reply_channel)
                return False
            
            # 处理多通道图像
            from config import PROJECT_SAVE_DIR
            outputDir = os.path.join(PROJECT_SAVE_DIR, str(projectId), imageName, "split")
            tileSize = 1024
            overlap = 0
            
            os.makedirs(outputDir, exist_ok=True)
            
            # 分离通道
            tiffPaths, outputDirs = channel_convert(mrxs_path, outputDir, projectId, imageName)
            print("通道转换完成")
            
            # 为每个通道生成深度缩放
            for i in range(len(tiffPaths)):
                os.makedirs(outputDirs[i], exist_ok=True)
                task = generate_deep_zoom_for_TIFF.apply_async(args=[tiffPaths[i], outputDirs[i], tileSize, overlap, taskId, imageId])
            
            return True
            
        except Exception as e:
            print(f"多通道图像处理异常: {e}")
            return False


# 全局消息处理器实例
message_handler = MessageHandler()
