"""
连接管理模块
负责管理RabbitMQ和Redis连接
"""
import contextlib
import pika
import pika.exceptions
import json
from config import (
    RABBITMQ_HOST, RABBITMQ_PORT, RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD,
    RABBITMQ_CONNECTION_TIMEOUT, RABBITMQ_HEARTBEAT, RABBITMQ_SOCKET_TIMEOUT,
    RABBITMQ_BLOCKED_CONNECTION_TIMEOUT, RABBITMQ_CONNECTION_ATTEMPTS, RABBITMQ_RETRY_DELAY
)
from .app import connect_redis


class ConnectionManager:
    """连接管理器，负责管理各种连接"""
    
    def __init__(self):
        self.redis_client = connect_redis()
        self.image_task_finish_callback_queue = 'medlabel_image_convert_task_finish_callback_queue'
    
    @contextlib.contextmanager
    def get_rabbitmq_connection(self):
        """RabbitMQ连接上下文管理器，确保连接正确关闭"""
        connection = None
        channel = None
        try:
            user_info = pika.PlainCredentials(RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD)
            # 添加连接参数以提高稳定性
            connection_params = pika.ConnectionParameters(
                host=RABBITMQ_HOST,
                port=RABBITMQ_PORT,
                virtual_host='/',
                credentials=user_info,
                heartbeat=RABBITMQ_HEARTBEAT,
                blocked_connection_timeout=RABBITMQ_BLOCKED_CONNECTION_TIMEOUT,
                connection_attempts=RABBITMQ_CONNECTION_ATTEMPTS,
                retry_delay=RABBITMQ_RETRY_DELAY,
                socket_timeout=RABBITMQ_SOCKET_TIMEOUT,
            )
            connection = pika.BlockingConnection(connection_params)
            channel = connection.channel()

            # 声明队列 - 注释掉队列声明功能
            # channel.queue_declare(
            #     queue=self.image_task_finish_callback_queue,
            #     durable=True,
            #     arguments={
            #         'x-dead-letter-exchange': 'dlx.direct',
            #         'x-dead-letter-routing-key': 'image_convert_task_finish_callback.dlq'
            #     }
            # )

            yield channel

        except Exception as e:
            print(f"RabbitMQ连接错误: {e}")
            raise
        finally:
            try:
                if channel and not channel.is_closed:
                    channel.close()
            except Exception as e:
                print(f"关闭channel时出错: {e}")
            try:
                if connection and not connection.is_closed:
                    connection.close()
            except Exception as e:
                print(f"关闭connection时出错: {e}")

    def send_progress_update(self, taskId, imageId, status, progress, result):
        """发送进度更新消息，使用连接管理器"""
        try:
            # 注释掉队列消息发送功能 - 停止向 medlabel_image_convert_task_finish_callback_queue 发送消息
            # with self.get_rabbitmq_connection() as channel:
            #     message = {
            #         "taskId": taskId,
            #         "imageId": imageId,
            #         "status": status,
            #         "progress": progress,
            #         "result": result
            #     }
            #     channel.basic_publish(
            #         exchange='',
            #         routing_key=self.image_task_finish_callback_queue,
            #         body=json.dumps(message),
            #         properties=pika.BasicProperties(
            #             delivery_mode=2,  # 消息持久化
            #         )
            #     )
            print(f"进度更新已发送: {taskId} - {progress} (队列消息发送已禁用)")
        except Exception as e:
            print(f"发送进度更新失败: {e}")
            # 不抛出异常，避免影响主任务

    def get_redis_client(self):
        """获取Redis客户端"""
        return self.redis_client


# 全局连接管理器实例
connection_manager = ConnectionManager()
