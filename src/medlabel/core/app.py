"""
简化的app模块，提供基本的连接功能和Celery应用
用于支持测试文件的运行和Celery worker启动
"""
import os
import redis
from celery import Celery
from kombu import Queue
from config import REDIS_HOST, REDIS_PORT, RABBITMQ_HOST, RABBITMQ_PORT, RABBITMQ_ACCOUNT, RABBITMQ_PASSWORD

def connect_redis():
    """创建Redis连接"""
    return redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        decode_responses=True,
        socket_connect_timeout=5,
        socket_timeout=5,
        retry_on_timeout=True
    )

# 创建Celery应用
celery = Celery('aiLabel')

# 定义队列配置，匹配现有队列的参数
task_queues = [
    # 匹配现有的medlabel_image_convert_queue队列配置
    Queue('medlabel_image_convert_queue',
          durable=True,
          queue_arguments={
              'x-dead-letter-exchange': 'dlx.direct',
              'x-dead-letter-routing-key': 'image_convert.dlq'
          }),
    # 标准队列
    Queue('ailabel_queue', durable=True),
]

# Celery配置
celery.conf.update(
    broker_url=f'pyamqp://{RABBITMQ_ACCOUNT}:{RABBITMQ_PASSWORD}@{RABBITMQ_HOST}:{RABBITMQ_PORT}//',
    result_backend=f'redis://{REDIS_HOST}:{REDIS_PORT}/0',
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    task_queues=task_queues,
    task_routes={
        'tasks.image_convert_task': {'queue': 'medlabel_image_convert_queue'},
        'tasks.test_task': {'queue': 'ailabel_queue'},
    },
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=30,
    task_default_queue='ailabel_queue',
)
