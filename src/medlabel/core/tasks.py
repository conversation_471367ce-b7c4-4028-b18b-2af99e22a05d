"""
Celery任务模块
定义所有异步处理任务
"""
import os
import openslide
import openslide.deepzoom
from billiard import Pool
from . import celery
from .connections import connection_manager
from ..processors.pathology_image import PathologyImageProcessor
from ..processors.multichannel_image import MultichannelImageProcessor
from ..utils.common import time_logger, generate_metadata, set_permissions


def process_tile(level, x, y, slide_path, level_dir, tileSize, overlap, taskId, totalNum, imageId):
    """处理单个切片并更新 Redis 进度"""
    try:
        processor = PathologyImageProcessor()
        slide = processor.get_slide(slide_path)
        dz_gen = openslide.deepzoom.DeepZoomGenerator(slide, tile_size=tileSize, overlap=overlap, limit_bounds=False)
        tile = dz_gen.get_tile(level, (x, y))

        tile_path = os.path.join(level_dir, f'{x}_{y}.jpeg')
        tile.save(tile_path, 'JPEG')

        redis_client = connection_manager.get_redis_client()
        redis_client.incr(f'image_convert_task_processed:{taskId}')
        processed_tiles = int(redis_client.get(f'image_convert_task_processed:{taskId}'))
        progress = round(processed_tiles / totalNum, 2)

        # 每处理500个瓦片发送一次进度更新
        if processed_tiles % 500 == 0:
            connection_manager.send_progress_update(
                taskId=taskId,
                imageId=imageId,
                status=1,
                progress=progress,
                result=f"{taskId}: 图像转化任务更新"
            )

    except Exception as e:
        print(f"Error processing tile {x}, {y}: {e}")


@celery.task
@time_logger
def generate_deep_zoom(mrxs_path, outputDir, tileSize, overlap, taskId, imageId):
    """Celery 任务，使用 billiard.Pool 进行多进程并行生成深度缩放"""
    try:
        processor = PathologyImageProcessor()
        slide = processor.get_slide(mrxs_path)
        os.makedirs(outputDir, exist_ok=True)
    except openslide.OpenSlideError as e:
        print(f"Cannot open slide at {mrxs_path}, {e}")
        return

    dz_gen = openslide.deepzoom.DeepZoomGenerator(slide, tile_size=tileSize, overlap=overlap, limit_bounds=False)
    totalNum = sum(x * y for x, y in dz_gen.level_tiles)

    # 获取图像尺寸
    width, height = slide.dimensions

    print(f"图像尺寸: {width} x {height}")
    print(f"计算切片总数为: {totalNum}")

    num_processes = max(1, 64)
    print(f"当前可使用进程数为: {str(num_processes)}")

    # 初始化 Redis 进度记录
    redis_client = connection_manager.get_redis_client()
    redis_client.set(f'image_convert_task_progress:{taskId}', '0')

    # 发送任务开始通知
    connection_manager.send_progress_update(
        taskId=taskId,
        imageId=imageId,
        status=1,
        progress=0.0,
        result=f"{taskId}: 开始生成深度图"
    )

    try:
        tasks = []

        # 生成 metadata.xml 文件
        with open(os.path.join(outputDir, f'metadata.xml'), 'w') as f:
            f.write(dz_gen.get_dzi('JPEG'))
        os.chmod(os.path.join(outputDir, f'metadata.xml'), 0o777)

        # 处理每个层级的切片
        for level in range(dz_gen.level_count):
            level_dir = os.path.join(outputDir, "imgs", str(level))
            os.makedirs(level_dir, exist_ok=True)
            tiles = dz_gen.level_tiles[level]

            for x in range(tiles[0]):
                for y in range(tiles[1]):
                    tasks.append((level, x, y, mrxs_path, level_dir, tileSize, overlap, taskId, totalNum, imageId))

            set_permissions(level_dir)

        with Pool(processes=num_processes) as pool:
            for args in tasks:
                pool.apply_async(process_tile, args=args)

            pool.close()
            pool.join()

        # 生成元数据
        generate_metadata(width, height, outputDir, tileSize)

        # 发送任务完成通知
        connection_manager.send_progress_update(
            taskId=taskId,
            imageId=imageId,
            status=2,
            progress=1.0,
            result=f"{taskId}: 图像转化任务完成"
        )

    except Exception as e:
        # 出现异常时发送错误回调
        print(f"在生成深度图时发生异常: {e}")
        connection_manager.send_progress_update(
            taskId=taskId,
            imageId=imageId,
            status=3,
            progress=0.0,
            result=f"图像转化任务失败: {str(e)}"
        )


@celery.task
@time_logger
def channel_convert(imageUrl, outputDir, projectId, imageName):
    """Celery 任务，处理多通道图像分离"""
    processor = MultichannelImageProcessor()
    return processor.split_channels(imageUrl, outputDir, projectId, imageName)


@celery.task
@time_logger
def generate_deep_zoom_for_TIFF(mrxs_path, outputDir, tileSize, overlap, taskId, imageId):
    """Celery 任务，为TIFF图像生成深度缩放"""
    processor = MultichannelImageProcessor()
    result = processor.generate_deep_zoom_for_tiff(mrxs_path, outputDir, tileSize, overlap, taskId, imageId)
    
    if not result.success:
        print(f"TIFF深度缩放生成失败: {result.message}")
    
    return result.success
