"""
简化的任务模块，用于Celery worker启动
"""
import json
import time
from src.medlabel.core.app import celery, connect_redis

@celery.task(bind=True)
def test_task(self, message):
    """测试任务"""
    print(f"收到测试任务: {message}")
    return {"status": "success", "message": message}

@celery.task(bind=True)
def image_convert_task(self, data):
    """图像转换任务 - 简化版本"""
    try:
        print(f"收到图像转换任务: {data}")
        
        # 模拟任务处理
        task_id = data.get('taskId', 'unknown')
        redis_client = connect_redis()
        
        # 设置初始进度
        redis_client.set(f'image_convert_task_progress:{task_id}', '0.1')
        
        # 模拟处理过程
        for i in range(1, 6):
            time.sleep(2)  # 模拟处理时间
            progress = i * 0.2
            redis_client.set(f'image_convert_task_progress:{task_id}', str(progress))
            print(f"任务 {task_id} 进度: {progress * 100}%")
        
        # 标记完成
        redis_client.set(f'image_convert_task_processed:{task_id}', '1')
        
        return {"status": "success", "taskId": task_id}
        
    except Exception as e:
        print(f"任务处理失败: {e}")
        return {"status": "error", "error": str(e)}

# 添加一个处理原始JSON消息的任务
@celery.task(bind=True, name='medlabel_image_convert_task')
def medlabel_image_convert_task(self, taskId, projectId, imageName, imageTypeId, imageId, imageUrl):
    """处理原始JSON消息格式的图像转换任务"""
    try:
        print(f"收到medlabel图像转换任务:")
        print(f"  taskId: {taskId}")
        print(f"  projectId: {projectId}")
        print(f"  imageName: {imageName}")
        print(f"  imageTypeId: {imageTypeId}")
        print(f"  imageId: {imageId}")
        print(f"  imageUrl: {imageUrl}")

        redis_client = connect_redis()

        # 设置初始进度
        redis_client.set(f'image_convert_task_progress:{taskId}', '0.1')

        # 模拟处理过程
        for i in range(1, 6):
            time.sleep(2)  # 模拟处理时间
            progress = i * 0.2
            redis_client.set(f'image_convert_task_progress:{taskId}', str(progress))
            print(f"任务 {taskId} 进度: {progress * 100}%")

        # 标记完成
        redis_client.set(f'image_convert_task_processed:{taskId}', '1')

        return {"status": "success", "taskId": taskId}

    except Exception as e:
        print(f"任务处理失败: {e}")
        return {"status": "error", "error": str(e)}

# 注册任务路由
celery.conf.task_routes = {
    'tasks.image_convert_task': {'queue': 'medlabel_image_convert_queue'},
    'tasks.test_task': {'queue': 'ailabel_queue'},
    'medlabel_image_convert_task': {'queue': 'medlabel_image_convert_queue'},
}
