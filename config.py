import os
from dotenv import load_dotenv

def seek_gpu():
    """简化的GPU选择函数，用于测试"""
    print("🎯 GPU选择功能已简化（测试模式）")


def load_environment_config():
    """加载环境配置文件"""
    celery_env = os.getenv('CELERY_ENV', 'prod')

    # 配置文件优先级
    config_files = [
        f'.env.{celery_env}',  # 指定环境配置
        '.env.prod',           # 生产环境默认
        '.env.dev'             # 开发环境备用
    ]

    for config_file in config_files:
        if os.path.exists(config_file):
            load_dotenv(config_file)
            print(f"🎯 已加载配置文件: {config_file}")
            return celery_env

    print("⚠️ 未找到环境配置文件，使用默认配置")
    return celery_env


# 加载环境配置
ENVIRONMENT = load_environment_config()

# 应用配置
HOST_UPLOADS = os.getenv('HOST_UPLOADS', 'http://**************:3030/uploads')
DEBUG_FLAG = os.getenv('DEBUG_FLAG', 'False').lower() == 'true'
PROJECT_SAVE_DIR = os.getenv('PROJECT_SAVE_DIR', '/nfs5/medlabel/medlabel_212/projects')

# RabbitMQ 配置
RABBITMQ_HOST = os.getenv('RABBITMQ_HOST', '**************')
RABBITMQ_PORT = int(os.getenv('RABBITMQ_PORT', '25675'))
RABBITMQ_ACCOUNT = os.getenv('RABBITMQ_ACCOUNT', 'admin')
RABBITMQ_PASSWORD = os.getenv('RABBITMQ_PASSWORD', 'vipa@404')
RABBITMQ_CONNECTION_TIMEOUT = int(os.getenv('RABBITMQ_CONNECTION_TIMEOUT', '60'))
RABBITMQ_HEARTBEAT = int(os.getenv('RABBITMQ_HEARTBEAT', '300'))
RABBITMQ_SOCKET_TIMEOUT = int(os.getenv('RABBITMQ_SOCKET_TIMEOUT', '10'))
RABBITMQ_BLOCKED_CONNECTION_TIMEOUT = int(os.getenv('RABBITMQ_BLOCKED_CONNECTION_TIMEOUT', '300'))
RABBITMQ_CONNECTION_ATTEMPTS = int(os.getenv('RABBITMQ_CONNECTION_ATTEMPTS', '3'))
RABBITMQ_RETRY_DELAY = int(os.getenv('RABBITMQ_RETRY_DELAY', '1'))

# Redis 配置
REDIS_HOST = os.getenv('REDIS_HOST', '**************')
REDIS_PORT = int(os.getenv('REDIS_PORT', '26379'))

# 初始化GPU
seek_gpu()

# 配置验证和日志
def validate_config():
    """验证关键配置项"""
    required_configs = {
        'RABBITMQ_HOST': RABBITMQ_HOST,
        'REDIS_HOST': REDIS_HOST,
        'PROJECT_SAVE_DIR': PROJECT_SAVE_DIR
    }

    missing = [k for k, v in required_configs.items() if not v]
    if missing:
        raise ValueError(f"缺少必要配置: {', '.join(missing)}")

    print(f"🎯 配置加载完成 - 环境: {'开发' if DEBUG_FLAG else '生产'}")
    print(f"   Redis: {REDIS_HOST}:{REDIS_PORT}")
    print(f"   RabbitMQ: {RABBITMQ_HOST}:{RABBITMQ_PORT}")
    print(f"   存储目录: {PROJECT_SAVE_DIR}")


# 执行配置验证
validate_config()
